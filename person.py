# Person Data Module
# Production-ready person data with robust tracking

import numpy as np
import time
from collections import deque

from config import (
    MIN_FRAMES_TO_CONFIRM, MIN_VECTORS_TO_CONFIRM, MAX_MISSING_FRAMES,
    SAFETY_CONFIRMATION_FRAMES, DEQUE_SIZES, QUALITY_WEIGHTS, SIMILARITY_WEIGHTS,
    TRACKING_OPTIMIZATION
)


class ProductionPerson:
    """Production-ready person data with robust tracking."""
    
    def __init__(self, track_id):
        self.track_id = track_id
        self.reid_features = deque(maxlen=DEQUE_SIZES['reid_features'])
        self.frame_count = 0
        self.missing_frames = 0
        self.first_seen = time.time()
        self.last_seen = time.time()
        
        # Quality tracking
        self.confidences = deque(maxlen=DEQUE_SIZES['confidences'])
        self.bbox_sizes = deque(maxlen=DEQUE_SIZES['bbox_sizes'])
        self.quality_scores = deque(maxlen=DEQUE_SIZES['quality_scores'])
        
        # Movement tracking
        self.positions = deque(maxlen=DEQUE_SIZES['positions'])
        self.velocities = deque(maxlen=DEQUE_SIZES['velocities'])
        
        # ReID status
        self.matched_to_person_id = None
        self.reid_confidence = 0.0

        # Optimization tracking
        self.frames_since_reid_update = 0
        self.total_observations = 0
        self.is_confirmed = False

        # Image storage for gallery
        self.best_image = None
        self.best_image_quality = 0.0
        self.last_image = None

        print(f"👤 Created person: ID {track_id}")
    
    def add_observation(self, image, bbox, confidence, reid_extractor):
        """Add new observation with quality assessment."""
        # Extract person image from bbox
        x1, y1, x2, y2 = map(int, bbox)
        person_image = image[y1:y2, x1:x2].copy()
        self.last_image = person_image

        # Extract ReID features
        reid_features = reid_extractor.extract_features(image, bbox)
        
        if reid_features is not None:
            self.reid_features.append(reid_features)
            
            # Calculate quality score
            quality = self._calculate_quality_score(bbox, confidence, reid_features)
            self.quality_scores.append(quality)

            # Store best quality image for gallery
            if quality > self.best_image_quality:
                self.best_image_quality = quality
                self.best_image = person_image.copy()

            print(f"📊 Person {self.track_id}: Features added (total: {len(self.reid_features)}, quality: {quality:.3f})")
        else:
            print(f"❌ Person {self.track_id}: Feature extraction failed")
        
        # Update tracking info
        x1, y1, x2, y2 = bbox
        center_x, center_y = (x1 + x2) / 2, (y1 + y2) / 2
        bbox_area = (x2 - x1) * (y2 - y1)
        
        # Calculate velocity if we have previous position
        if len(self.positions) > 0:
            prev_x, prev_y = self.positions[-1]
            velocity = np.sqrt((center_x - prev_x)**2 + (center_y - prev_y)**2)
            self.velocities.append(velocity)
        
        self.positions.append((center_x, center_y))
        self.confidences.append(confidence)
        self.bbox_sizes.append(bbox_area)
        
        # Update counters
        self.frame_count += 1
        self.total_observations += 1
        self.frames_since_reid_update += 1
        self.missing_frames = 0
        self.last_seen = time.time()

        # Reset ReID update counter if we extracted features
        if reid_features is not None:
            self.frames_since_reid_update = 0

    def add_lightweight_observation(self, image, bbox, confidence):
        """Add lightweight observation without ReID feature extraction (for confirmed persons)."""
        # Extract and store person image
        x1, y1, x2, y2 = map(int, bbox)
        person_image = image[y1:y2, x1:x2].copy()
        self.last_image = person_image

        # Update tracking info without expensive ReID processing
        center_x, center_y = (x1 + x2) / 2, (y1 + y2) / 2
        bbox_area = (x2 - x1) * (y2 - y1)

        # Calculate velocity if we have previous position
        if len(self.positions) > 0:
            prev_x, prev_y = self.positions[-1]
            velocity = np.sqrt((center_x - prev_x)**2 + (center_y - prev_y)**2)
            self.velocities.append(velocity)

        self.positions.append((center_x, center_y))
        self.confidences.append(confidence)
        self.bbox_sizes.append(bbox_area)

        # Update counters
        self.frame_count += 1
        self.total_observations += 1
        self.frames_since_reid_update += 1
        self.missing_frames = 0
        self.last_seen = time.time()

        print(f"⚡ Person {self.track_id}: Lightweight update (frames since ReID: {self.frames_since_reid_update})")

    def should_update_reid_features(self):
        """Check if ReID features should be updated for confirmed person."""
        if not TRACKING_OPTIMIZATION['skip_reid_for_confirmed']:
            return True

        if not self.is_confirmed:
            return True

        # Update ReID features periodically even for confirmed persons
        return self.frames_since_reid_update >= TRACKING_OPTIMIZATION['confirmed_update_interval']

    def _calculate_quality_score(self, bbox, confidence, features):
        """Calculate observation quality score."""
        # Detection confidence factor
        conf_factor = confidence
        
        # Bbox size factor (larger = better)
        x1, y1, x2, y2 = bbox
        bbox_area = (x2 - x1) * (y2 - y1)
        size_factor = min(bbox_area / (150 * 300), 1.0)  # Normalize to reasonable person size
        
        # Feature quality factor (feature magnitude)
        feature_factor = min(np.linalg.norm(features), 1.0)
        
        # Combine factors using configured weights
        quality = (QUALITY_WEIGHTS['confidence_factor'] * conf_factor + 
                  QUALITY_WEIGHTS['size_factor'] * size_factor + 
                  QUALITY_WEIGHTS['feature_factor'] * feature_factor)
        return quality
    
    def mark_missing(self):
        """Mark person as missing in current frame."""
        self.missing_frames += 1
    
    def is_ready_for_decision(self):
        """Check if person has enough data for uniqueness decision."""
        ready = (self.frame_count >= MIN_FRAMES_TO_CONFIRM and 
                len(self.reid_features) >= MIN_VECTORS_TO_CONFIRM)
        
        if ready:
            print(f"✅ Person {self.track_id}: Ready for decision ({self.frame_count}F, {len(self.reid_features)}V)")
        
        return ready
    
    def should_safety_confirm(self):
        """Check if person should be safety confirmed before leaving."""
        return (self.frame_count >= SAFETY_CONFIRMATION_FRAMES and 
                len(self.reid_features) >= 1 and
                self.missing_frames >= MAX_MISSING_FRAMES)
    
    def should_be_removed(self):
        """Check if person should be removed (too long missing)."""
        return self.missing_frames >= MAX_MISSING_FRAMES
    
    def get_robust_reid_features(self):
        """Get robust ReID features using quality weighting."""
        if len(self.reid_features) == 0:
            return None
        
        # Use quality-weighted average
        num_features = len(self.reid_features)
        if len(self.quality_scores) >= num_features:
            weights = np.array(list(self.quality_scores)[-num_features:])
            weights = weights / (np.sum(weights) + 1e-8)  # Normalize weights
            
            # Calculate weighted average
            features_array = np.array(list(self.reid_features))
            robust_features = np.average(features_array, axis=0, weights=weights)
        else:
            # Simple average if quality scores not available
            robust_features = np.mean(list(self.reid_features), axis=0)
        
        # Final normalization
        norm = np.linalg.norm(robust_features)
        if norm > 0:
            robust_features = robust_features / norm
        
        return robust_features
    
    def similarity_to(self, other_person):
        """Calculate similarity to another person using ReID features."""
        my_features = self.get_robust_reid_features()
        other_features = other_person.get_robust_reid_features()
        
        if my_features is None or other_features is None:
            return 0.0
        
        # Primary similarity: cosine similarity of ReID features
        cosine_sim = np.dot(my_features, other_features)
        
        # Quality factor: higher quality comparisons are more trustworthy
        my_quality = np.mean(list(self.quality_scores)) if self.quality_scores else 0.5
        other_quality = np.mean(list(other_person.quality_scores)) if other_person.quality_scores else 0.5
        quality_factor = (my_quality + other_quality) / 2.0
        
        # Size consistency factor
        size_factor = self._calculate_size_consistency(other_person)
        
        # Combined similarity with configured weights
        final_similarity = (
            SIMILARITY_WEIGHTS['cosine_similarity'] * cosine_sim +
            SIMILARITY_WEIGHTS['quality_factor'] * quality_factor +
            SIMILARITY_WEIGHTS['size_factor'] * size_factor
        )
        
        return max(0.0, min(1.0, final_similarity))
    
    def _calculate_size_consistency(self, other_person):
        """Calculate size consistency between persons."""
        if len(self.bbox_sizes) == 0 or len(other_person.bbox_sizes) == 0:
            return 0.5
        
        my_avg_size = np.mean(list(self.bbox_sizes))
        other_avg_size = np.mean(list(other_person.bbox_sizes))
        
        # Size similarity (closer sizes = higher score)
        size_ratio = min(my_avg_size, other_avg_size) / max(my_avg_size, other_avg_size)
        return size_ratio
    
    def get_summary(self):
        """Get person summary for debugging."""
        avg_quality = np.mean(list(self.quality_scores)) if self.quality_scores else 0.0
        avg_confidence = np.mean(list(self.confidences)) if self.confidences else 0.0
        
        return {
            'track_id': self.track_id,
            'frame_count': self.frame_count,
            'reid_vectors': len(self.reid_features),
            'avg_quality': avg_quality,
            'avg_confidence': avg_confidence,
            'duration': self.last_seen - self.first_seen,
            'missing_frames': self.missing_frames
        }
