#!/usr/bin/env python3
"""
Comprehensive comparison between our custom implementation and deep-person-reid
"""

import sys
import os
import cv2
import numpy as np
import torch
import time
from pathlib import Path

# Add paths
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import libraries
try:
    import torchreid
    from torchreid.utils import FeatureExtractor
    TORCHREID_AVAILABLE = True
    print("✅ TorchReID library available")
except ImportError as e:
    print(f"❌ TorchReID library not available: {e}")
    TORCHREID_AVAILABLE = False

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
    print("✅ YOLO available")
except ImportError as e:
    print(f"❌ YOLO not available: {e}")
    YOLO_AVAILABLE = False

# Try to import our custom implementation
try:
    sys.path.append('../current_progress_backup')
    from tracker import PersonTracker as CustomTracker
    from config import Config
    CUSTOM_AVAILABLE = True
    print("✅ Custom implementation available")
except ImportError as e:
    print(f"❌ Custom implementation not available: {e}")
    CUSTOM_AVAILABLE = False


class DeepReIDTracker:
    """Tracker using deep-person-reid library"""
    
    def __init__(self, model_name='osnet_x1_0'):
        self.model_name = model_name
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # Initialize YOLO
        if YOLO_AVAILABLE:
            self.yolo = YOLO('../models/yolov8n.pt')
        else:
            self.yolo = None
        
        # Initialize ReID extractor
        if TORCHREID_AVAILABLE:
            try:
                self.reid_extractor = FeatureExtractor(
                    model_name=model_name,
                    model_path='',
                    device=self.device,
                    verbose=False
                )
                print(f"✅ Deep ReID model '{model_name}' loaded")
            except Exception as e:
                print(f"❌ Failed to load deep ReID model: {e}")
                self.reid_extractor = None
        else:
            self.reid_extractor = None
        
        # Tracking data
        self.persons = {}
        self.next_id = 1
        self.frame_count = 0
        self.similarity_threshold = 0.7
        
    def extract_features(self, image, bbox):
        """Extract ReID features"""
        if self.reid_extractor is None:
            return None
        
        try:
            x1, y1, x2, y2 = map(int, bbox)
            crop = image[y1:y2, x1:x2]
            if crop.size == 0:
                return None
            
            crop_rgb = cv2.cvtColor(crop, cv2.COLOR_BGR2RGB)
            features = self.reid_extractor(crop_rgb)
            return features.cpu().numpy().flatten()
        except Exception as e:
            return None
    
    def compute_similarity(self, feat1, feat2):
        """Compute cosine similarity"""
        if feat1 is None or feat2 is None:
            return 0.0
        
        feat1_norm = feat1 / (np.linalg.norm(feat1) + 1e-8)
        feat2_norm = feat2 / (np.linalg.norm(feat2) + 1e-8)
        return np.dot(feat1_norm, feat2_norm)
    
    def process_frame(self, frame):
        """Process frame and return tracking results"""
        self.frame_count += 1
        
        if self.yolo is None:
            return []
        
        # Detect persons
        results = self.yolo(frame, classes=[0], verbose=False)
        detections = []
        
        if len(results) > 0 and results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            
            for box, conf in zip(boxes, confidences):
                if conf > 0.5:
                    detections.append((box, conf))
        
        # Process detections
        tracked_persons = []
        
        for bbox, conf in detections:
            features = self.extract_features(frame, bbox)
            
            # Find best match
            best_id = None
            best_sim = 0.0
            
            for person_id, person_data in self.persons.items():
                if len(person_data['features']) > 0:
                    avg_features = np.mean(person_data['features'], axis=0)
                    sim = self.compute_similarity(features, avg_features)
                    if sim > best_sim and sim > self.similarity_threshold:
                        best_sim = sim
                        best_id = person_id
            
            if best_id is not None:
                person_id = best_id
            else:
                person_id = self.next_id
                self.next_id += 1
                self.persons[person_id] = {'features': [], 'last_seen': 0}
            
            # Update person
            if features is not None:
                self.persons[person_id]['features'].append(features)
                if len(self.persons[person_id]['features']) > 10:
                    self.persons[person_id]['features'].pop(0)
            
            self.persons[person_id]['last_seen'] = self.frame_count
            
            tracked_persons.append({
                'id': person_id,
                'bbox': bbox,
                'confidence': conf,
                'similarity': best_sim if best_id else 0.0
            })
        
        # Remove old persons
        to_remove = []
        for person_id, person_data in self.persons.items():
            if self.frame_count - person_data['last_seen'] > 30:
                to_remove.append(person_id)
        
        for person_id in to_remove:
            del self.persons[person_id]
        
        return tracked_persons


def create_demo_video():
    """Create a simple demo video with moving rectangles representing people"""
    print("🎬 Creating demo video...")
    
    width, height = 640, 480
    fps = 30
    duration = 10  # seconds
    total_frames = fps * duration
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('demo_video.mp4', fourcc, fps, (width, height))
    
    # Create moving "persons" (colored rectangles)
    persons = [
        {'color': (0, 0, 255), 'start_x': 50, 'start_y': 200, 'speed_x': 2, 'speed_y': 0},   # Red person
        {'color': (0, 255, 0), 'start_x': 100, 'start_y': 150, 'speed_x': 1, 'speed_y': 1}, # Green person
        {'color': (255, 0, 0), 'start_x': 200, 'start_y': 300, 'speed_x': -1, 'speed_y': -1}, # Blue person
    ]
    
    for frame_num in range(total_frames):
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        for person in persons:
            x = person['start_x'] + person['speed_x'] * frame_num
            y = person['start_y'] + person['speed_y'] * frame_num
            
            # Bounce off walls
            if x < 0 or x > width - 80:
                person['speed_x'] *= -1
            if y < 0 or y > height - 120:
                person['speed_y'] *= -1
            
            x = max(0, min(width - 80, x))
            y = max(0, min(height - 120, y))
            
            # Draw person (rectangle)
            cv2.rectangle(frame, (int(x), int(y)), (int(x + 80), int(y + 120)), person['color'], -1)
            
            # Add some texture
            cv2.rectangle(frame, (int(x + 10), int(y + 10)), (int(x + 70), int(y + 40)), 
                         tuple(int(c * 0.7) for c in person['color']), -1)  # Head
        
        out.write(frame)
    
    out.release()
    print("✅ Demo video created: demo_video.mp4")
    return 'demo_video.mp4'


def run_comparison():
    """Run comparison between implementations"""
    print("\n🔄 Running Implementation Comparison")
    print("=" * 50)
    
    # Create demo video if no video file exists
    video_path = 'demo_video.mp4'
    if not os.path.exists(video_path):
        video_path = create_demo_video()
    
    # Initialize trackers
    trackers = {}
    
    if TORCHREID_AVAILABLE and YOLO_AVAILABLE:
        try:
            trackers['Deep ReID (OSNet x1.0)'] = DeepReIDTracker('osnet_x1_0')
            trackers['Deep ReID (OSNet x0.5)'] = DeepReIDTracker('osnet_x0_5')
        except Exception as e:
            print(f"❌ Failed to initialize deep ReID trackers: {e}")
    
    if CUSTOM_AVAILABLE:
        try:
            config = Config()
            trackers['Custom Implementation'] = CustomTracker(config)
        except Exception as e:
            print(f"❌ Failed to initialize custom tracker: {e}")
    
    if not trackers:
        print("❌ No trackers available for comparison!")
        return
    
    print(f"🎯 Comparing {len(trackers)} implementations:")
    for name in trackers.keys():
        print(f"  - {name}")
    
    # Process video with each tracker
    results = {}
    
    for tracker_name, tracker in trackers.items():
        print(f"\n🔍 Testing {tracker_name}...")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Cannot open video: {video_path}")
            continue
        
        frame_count = 0
        processing_times = []
        unique_persons = set()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Process frame
            start_time = time.time()
            
            if hasattr(tracker, 'process_frame'):
                tracked_persons = tracker.process_frame(frame)
            else:
                # Custom tracker interface
                tracked_persons = []
                # Add custom tracker processing here if needed
            
            processing_time = time.time() - start_time
            processing_times.append(processing_time)
            
            # Collect unique person IDs
            for person in tracked_persons:
                unique_persons.add(person['id'])
            
            # Show progress every 30 frames
            if frame_count % 30 == 0:
                avg_time = np.mean(processing_times[-30:])
                fps = 1.0 / avg_time if avg_time > 0 else 0
                print(f"  Frame {frame_count}: {fps:.1f} FPS, {len(unique_persons)} unique persons")
        
        cap.release()
        
        # Store results
        avg_processing_time = np.mean(processing_times) if processing_times else 0
        results[tracker_name] = {
            'frames_processed': frame_count,
            'avg_processing_time': avg_processing_time,
            'avg_fps': 1.0 / avg_processing_time if avg_processing_time > 0 else 0,
            'unique_persons': len(unique_persons),
            'total_processing_time': sum(processing_times)
        }
    
    # Display comparison results
    print(f"\n📊 Comparison Results")
    print("=" * 60)
    
    for tracker_name, result in results.items():
        print(f"\n{tracker_name}:")
        print(f"  Frames processed: {result['frames_processed']}")
        print(f"  Average processing time: {result['avg_processing_time']*1000:.1f}ms")
        print(f"  Average FPS: {result['avg_fps']:.1f}")
        print(f"  Unique persons detected: {result['unique_persons']}")
        print(f"  Total processing time: {result['total_processing_time']:.2f}s")
    
    # Find best performer
    if results:
        best_fps = max(results.values(), key=lambda x: x['avg_fps'])
        best_accuracy = max(results.values(), key=lambda x: x['unique_persons'])
        
        print(f"\n🏆 Performance Winners:")
        for name, result in results.items():
            if result['avg_fps'] == best_fps['avg_fps']:
                print(f"  Fastest: {name} ({result['avg_fps']:.1f} FPS)")
            if result['unique_persons'] == best_accuracy['unique_persons']:
                print(f"  Most accurate: {name} ({result['unique_persons']} persons)")


def main():
    """Main function"""
    print("🚀 Deep Person ReID vs Custom Implementation Comparison")
    print("=" * 60)
    
    print(f"Environment:")
    print(f"  PyTorch: {torch.__version__}")
    print(f"  CUDA available: {torch.cuda.is_available()}")
    print(f"  Device: {'cuda' if torch.cuda.is_available() else 'cpu'}")
    
    run_comparison()
    
    print(f"\n✅ Comparison completed!")
    print("The demo video 'demo_video.mp4' was created for testing.")


if __name__ == "__main__":
    main()
