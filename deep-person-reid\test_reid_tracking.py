#!/usr/bin/env python3
"""
Test script for deep-person-reid with person tracking
Compares the deep-person-reid library with our custom implementation
"""

import sys
import os
import cv2
import numpy as np
import torch
import time
from pathlib import Path

# Add the torchreid path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

try:
    import torchreid
    from torchreid.utils import FeatureExtractor
    TORCHREID_AVAILABLE = True
    print("✅ TorchReID library loaded successfully!")
except ImportError as e:
    print(f"❌ TorchReID library not available: {e}")
    TORCHREID_AVAILABLE = False

from ultralytics import YOLO


class DeepPersonReIDTracker:
    """Person tracker using deep-person-reid library"""
    
    def __init__(self, model_name='osnet_x1_0', device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        self.model_name = model_name
        
        # Initialize YOLO for person detection
        self.yolo = YOLO('../models/yolov8n.pt')
        
        # Initialize ReID feature extractor
        if TORCHREID_AVAILABLE:
            try:
                self.reid_extractor = FeatureExtractor(
                    model_name=model_name,
                    model_path='',  # Use pretrained model
                    device=device,
                    verbose=True
                )
                print(f"✅ ReID model '{model_name}' loaded successfully!")
            except Exception as e:
                print(f"❌ Failed to load ReID model: {e}")
                self.reid_extractor = None
        else:
            self.reid_extractor = None
        
        # Person tracking data
        self.persons = {}  # person_id -> {'features': [], 'last_seen': frame_count, 'bbox': []}
        self.next_person_id = 1
        self.frame_count = 0
        self.similarity_threshold = 0.7
        
    def extract_features(self, image, bbox):
        """Extract ReID features from person crop"""
        if self.reid_extractor is None:
            return None
            
        try:
            x1, y1, x2, y2 = map(int, bbox)
            person_crop = image[y1:y2, x1:x2]
            
            if person_crop.size == 0:
                return None
                
            # Convert BGR to RGB
            person_crop_rgb = cv2.cvtColor(person_crop, cv2.COLOR_BGR2RGB)
            
            # Extract features
            features = self.reid_extractor(person_crop_rgb)
            return features.cpu().numpy().flatten()
            
        except Exception as e:
            print(f"Feature extraction error: {e}")
            return None
    
    def compute_similarity(self, features1, features2):
        """Compute cosine similarity between two feature vectors"""
        if features1 is None or features2 is None:
            return 0.0
        
        # Normalize features
        features1 = features1 / (np.linalg.norm(features1) + 1e-8)
        features2 = features2 / (np.linalg.norm(features2) + 1e-8)
        
        # Compute cosine similarity
        similarity = np.dot(features1, features2)
        return similarity
    
    def find_matching_person(self, features):
        """Find matching person based on ReID features"""
        if features is None:
            return None
            
        best_match_id = None
        best_similarity = 0.0
        
        for person_id, person_data in self.persons.items():
            if len(person_data['features']) == 0:
                continue
                
            # Compare with average features
            avg_features = np.mean(person_data['features'], axis=0)
            similarity = self.compute_similarity(features, avg_features)
            
            if similarity > best_similarity and similarity > self.similarity_threshold:
                best_similarity = similarity
                best_match_id = person_id
        
        return best_match_id, best_similarity if best_match_id else (None, 0.0)
    
    def update_person(self, person_id, features, bbox):
        """Update person data"""
        if person_id not in self.persons:
            self.persons[person_id] = {'features': [], 'last_seen': 0, 'bbox': []}
        
        person_data = self.persons[person_id]
        
        # Add features (keep only last 10 for efficiency)
        if features is not None:
            person_data['features'].append(features)
            if len(person_data['features']) > 10:
                person_data['features'].pop(0)
        
        person_data['last_seen'] = self.frame_count
        person_data['bbox'] = bbox
    
    def process_frame(self, frame):
        """Process a single frame"""
        self.frame_count += 1
        
        # Detect persons using YOLO
        results = self.yolo(frame, classes=[0], verbose=False)  # class 0 is person
        
        detections = []
        if len(results) > 0 and results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            
            for box, conf in zip(boxes, confidences):
                if conf > 0.5:  # Confidence threshold
                    detections.append((box, conf))
        
        # Process each detection
        active_persons = []
        
        for bbox, conf in detections:
            # Extract ReID features
            features = self.extract_features(frame, bbox)
            
            # Find matching person
            match_id, similarity = self.find_matching_person(features)
            
            if match_id is not None:
                # Update existing person
                person_id = match_id
                self.update_person(person_id, features, bbox)
                print(f"🔗 ReID Match: Person {person_id} (similarity: {similarity:.3f})")
            else:
                # Create new person
                person_id = self.next_person_id
                self.next_person_id += 1
                self.update_person(person_id, features, bbox)
                print(f"🆕 New Person: ID {person_id}")
            
            active_persons.append(person_id)
            
            # Draw bounding box and ID
            x1, y1, x2, y2 = map(int, bbox)
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.putText(frame, f'Person {person_id}', (x1, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # Show confidence and similarity if available
            info_text = f'Conf: {conf:.2f}'
            if match_id is not None:
                info_text += f', Sim: {similarity:.2f}'
            cv2.putText(frame, info_text, (x1, y2+20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Remove old persons (not seen for 30 frames)
        persons_to_remove = []
        for person_id, person_data in self.persons.items():
            if self.frame_count - person_data['last_seen'] > 30:
                persons_to_remove.append(person_id)
        
        for person_id in persons_to_remove:
            del self.persons[person_id]
            print(f"🗑️ Removed Person {person_id} (not seen for 30 frames)")
        
        # Display statistics
        stats_text = [
            f"Frame: {self.frame_count}",
            f"Active Persons: {len(active_persons)}",
            f"Total Persons: {len(self.persons)}",
            f"Model: {self.model_name}",
            f"Device: {self.device}"
        ]
        
        for i, text in enumerate(stats_text):
            cv2.putText(frame, text, (10, 30 + i*25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return frame


def main():
    """Main function to run the tracker"""
    print("🚀 Starting Deep Person ReID Tracker Test")
    print("=" * 50)
    
    if not TORCHREID_AVAILABLE:
        print("❌ TorchReID not available. Please install it first.")
        return
    
    # Initialize tracker
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # Try different models
    models_to_test = ['osnet_x1_0', 'osnet_x0_75', 'osnet_x0_5']
    
    for model_name in models_to_test:
        print(f"\n🧪 Testing model: {model_name}")
        try:
            tracker = DeepPersonReIDTracker(model_name=model_name, device=device)
            
            # Test with webcam
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                print("❌ Cannot open webcam")
                continue
            
            print("📹 Press 'q' to quit, 's' to switch to next model")
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process frame
                start_time = time.time()
                processed_frame = tracker.process_frame(frame)
                processing_time = time.time() - start_time
                
                # Show FPS
                fps = 1.0 / processing_time if processing_time > 0 else 0
                cv2.putText(processed_frame, f'FPS: {fps:.1f}', (10, 180), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                
                cv2.imshow(f'Deep Person ReID - {model_name}', processed_frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    cap.release()
                    cv2.destroyAllWindows()
                    return
                elif key == ord('s'):
                    break
            
            cap.release()
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"❌ Error testing {model_name}: {e}")
            continue
    
    print("\n✅ Testing completed!")


if __name__ == "__main__":
    main()
