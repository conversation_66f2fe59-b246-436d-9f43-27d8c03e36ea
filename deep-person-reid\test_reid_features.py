#!/usr/bin/env python3
"""
Test script to compare ReID features between our implementation and deep-person-reid
"""

import sys
import os
import cv2
import numpy as np
import torch
import time
from pathlib import Path

# Add the torchreid path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

try:
    import torchreid
    from torchreid.utils import FeatureExtractor
    TORCHREID_AVAILABLE = True
    print("✅ TorchReID library loaded successfully!")
except ImportError as e:
    print(f"❌ TorchReID library not available: {e}")
    TORCHREID_AVAILABLE = False

# Add our custom implementation path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from reid_extractor import ReIDExtractor as CustomReIDExtractor
    CUSTOM_REID_AVAILABLE = True
    print("✅ Custom ReID implementation loaded successfully!")
except ImportError as e:
    print(f"❌ Custom ReID implementation not available: {e}")
    CUSTOM_REID_AVAILABLE = False


def create_synthetic_person_images():
    """Create synthetic person images for testing"""
    images = []
    
    # Create 6 different synthetic person images
    colors = [
        (255, 0, 0),    # Red person
        (0, 255, 0),    # Green person  
        (0, 0, 255),    # Blue person
        (255, 255, 0),  # Yellow person
        (255, 0, 255),  # Magenta person
        (0, 255, 255),  # Cyan person
    ]
    
    for i, color in enumerate(colors):
        # Create a 256x128 image (standard ReID input size)
        img = np.zeros((256, 128, 3), dtype=np.uint8)
        
        # Fill with base color
        img[:] = color
        
        # Add some patterns to make it more realistic
        # Head region (darker)
        head_color = tuple(int(c * 0.7) for c in color)
        img[0:50, :] = head_color
        
        # Torso region (original color)
        img[50:150, :] = color
        
        # Legs region (slightly different shade)
        leg_color = tuple(int(c * 0.8) for c in color)
        img[150:256, :] = leg_color
        
        # Add some noise for realism
        noise = np.random.randint(-20, 20, img.shape, dtype=np.int16)
        img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        images.append(img)
        
        # Create a slightly modified version of the same person
        img_variant = img.copy()
        # Add slight brightness variation
        brightness_change = np.random.randint(-30, 30)
        img_variant = np.clip(img_variant.astype(np.int16) + brightness_change, 0, 255).astype(np.uint8)
        images.append(img_variant)
    
    return images


def test_reid_features():
    """Test and compare ReID features from both implementations"""
    print("\n🧪 Testing ReID Feature Extraction")
    print("=" * 50)
    
    # Create synthetic person images
    print("📸 Creating synthetic person images...")
    images = create_synthetic_person_images()
    print(f"Created {len(images)} synthetic person images")
    
    # Initialize extractors
    extractors = {}
    
    # Initialize TorchReID extractor
    if TORCHREID_AVAILABLE:
        try:
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            extractors['torchreid_osnet_x1_0'] = FeatureExtractor(
                model_name='osnet_x1_0',
                model_path='',
                device=device,
                verbose=False
            )
            print("✅ TorchReID OSNet x1.0 extractor initialized")
            
            extractors['torchreid_osnet_x0_5'] = FeatureExtractor(
                model_name='osnet_x0_5', 
                model_path='',
                device=device,
                verbose=False
            )
            print("✅ TorchReID OSNet x0.5 extractor initialized")
            
        except Exception as e:
            print(f"❌ Failed to initialize TorchReID extractors: {e}")
    
    # Initialize custom extractor
    if CUSTOM_REID_AVAILABLE:
        try:
            extractors['custom_reid'] = CustomReIDExtractor()
            print("✅ Custom ReID extractor initialized")
        except Exception as e:
            print(f"❌ Failed to initialize custom ReID extractor: {e}")
    
    if not extractors:
        print("❌ No ReID extractors available!")
        return
    
    # Extract features for all images
    print(f"\n🔍 Extracting features using {len(extractors)} extractors...")
    
    all_features = {}
    extraction_times = {}
    
    for extractor_name, extractor in extractors.items():
        print(f"\n--- {extractor_name} ---")
        features = []
        times = []
        
        for i, img in enumerate(images):
            try:
                start_time = time.time()
                
                if 'torchreid' in extractor_name:
                    # TorchReID expects RGB
                    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    feat = extractor(img_rgb)
                    feat = feat.cpu().numpy().flatten()
                else:
                    # Custom extractor expects BGR
                    feat = extractor.extract_features(img)
                
                extraction_time = time.time() - start_time
                times.append(extraction_time)
                features.append(feat)
                
                print(f"  Image {i+1}: {feat.shape} features, {extraction_time*1000:.1f}ms")
                
            except Exception as e:
                print(f"  ❌ Error extracting features for image {i+1}: {e}")
                features.append(None)
                times.append(0)
        
        all_features[extractor_name] = features
        extraction_times[extractor_name] = times
        
        avg_time = np.mean([t for t in times if t > 0])
        print(f"  Average extraction time: {avg_time*1000:.1f}ms")
    
    # Compare feature similarities
    print(f"\n📊 Feature Similarity Analysis")
    print("=" * 50)
    
    def compute_cosine_similarity(feat1, feat2):
        if feat1 is None or feat2 is None:
            return 0.0
        feat1_norm = feat1 / (np.linalg.norm(feat1) + 1e-8)
        feat2_norm = feat2 / (np.linalg.norm(feat2) + 1e-8)
        return np.dot(feat1_norm, feat2_norm)
    
    # Test same person similarity (original vs variant)
    print("\n🔗 Same Person Similarity (Original vs Variant):")
    for extractor_name, features in all_features.items():
        print(f"\n{extractor_name}:")
        same_person_similarities = []
        
        for i in range(0, len(features), 2):  # Every pair is same person
            if i+1 < len(features) and features[i] is not None and features[i+1] is not None:
                sim = compute_cosine_similarity(features[i], features[i+1])
                same_person_similarities.append(sim)
                print(f"  Person {i//2 + 1}: {sim:.3f}")
        
        if same_person_similarities:
            avg_same = np.mean(same_person_similarities)
            print(f"  Average same-person similarity: {avg_same:.3f}")
    
    # Test different person similarity
    print(f"\n🚫 Different Person Similarity:")
    for extractor_name, features in all_features.items():
        print(f"\n{extractor_name}:")
        diff_person_similarities = []
        
        # Compare person 1 with persons 2, 3, etc.
        if features[0] is not None:
            for i in range(2, len(features), 2):  # Skip variants, compare different persons
                if features[i] is not None:
                    sim = compute_cosine_similarity(features[0], features[i])
                    diff_person_similarities.append(sim)
                    print(f"  Person 1 vs Person {i//2 + 1}: {sim:.3f}")
        
        if diff_person_similarities:
            avg_diff = np.mean(diff_person_similarities)
            print(f"  Average different-person similarity: {avg_diff:.3f}")
    
    # Performance comparison
    print(f"\n⚡ Performance Comparison:")
    print("=" * 50)
    for extractor_name, times in extraction_times.items():
        valid_times = [t for t in times if t > 0]
        if valid_times:
            avg_time = np.mean(valid_times)
            fps = 1.0 / avg_time if avg_time > 0 else 0
            print(f"{extractor_name}:")
            print(f"  Average time: {avg_time*1000:.1f}ms")
            print(f"  Theoretical FPS: {fps:.1f}")
    
    # Feature dimension comparison
    print(f"\n📏 Feature Dimensions:")
    print("=" * 50)
    for extractor_name, features in all_features.items():
        valid_features = [f for f in features if f is not None]
        if valid_features:
            dim = valid_features[0].shape[0]
            print(f"{extractor_name}: {dim}D features")
    
    # Save sample images for visual inspection
    print(f"\n💾 Saving sample images...")
    os.makedirs('sample_images', exist_ok=True)
    for i, img in enumerate(images[:6]):  # Save first 6 images
        cv2.imwrite(f'sample_images/person_{i//2 + 1}_variant_{i%2 + 1}.jpg', img)
    print(f"Saved {min(6, len(images))} sample images to 'sample_images/' directory")


def main():
    """Main function"""
    print("🚀 Deep Person ReID Feature Comparison Test")
    print("=" * 60)
    
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name()}")
    
    test_reid_features()
    
    print(f"\n✅ Feature comparison test completed!")
    print("Check the 'sample_images/' directory to see the synthetic person images used for testing.")


if __name__ == "__main__":
    main()
