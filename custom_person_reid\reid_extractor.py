# ReID Feature Extractor Module
# Production ReID extractor with real pre-trained weights and fallbacks

import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
import torchvision.models as models
import requests
import ssl
from pathlib import Path

from config import (
    device, REID_FEATURE_DIM, REID_MODEL_URLS, WEIGHTS_DIR,
    REID_IMAGE_SIZE, PERSON_RESIZE_SIZE, IMAGENET_MEAN, IMAGENET_STD,
    COLOR_FEATURE_CONFIG
)


class ProductionReIDExtractor:
    """Production ReID extractor with real pre-trained weights and fallbacks."""
    
    def __init__(self):
        print("🚀 Initializing Production ReID Extractor...")
        print("🔄 Trying multiple pre-trained options...")
        
        self.model = None
        self.model_type = None
        self.feature_dim = REID_FEATURE_DIM
        
        # Try different pre-trained options in order of preference
        self._initialize_model()
        
        # Image preprocessing
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(REID_IMAGE_SIZE),  # Standard ReID size
            transforms.ToTensor(),
            transforms.Normalize(mean=IMAGENET_MEAN, std=IMAGENET_STD)
        ])
        
        print(f"✅ Production ReID ready! Model: {self.model_type}")
        print(f"   📊 Feature dimension: {self.feature_dim}D")
    
    def _initialize_model(self):
        """Initialize model with multiple fallback options."""

        # Option 1: Try downloading real ReID weights (BEST)
        if self._try_download_reid_weights():
            return

        # Option 2: Try ResNet50 with ImageNet pre-training + ReID head
        if self._try_resnet50_pretrained():
            return

        # Option 3: Fallback to color-based features
        self._fallback_to_color_features()
    
    def _try_resnet50_pretrained(self):
        """Try ResNet50 with ImageNet pre-training."""
        try:
            print("📥 Loading ResNet50 with ImageNet pre-training...")
            
            # Load ResNet50 backbone with ImageNet weights
            backbone = models.resnet50(pretrained=True)
            
            # Replace final layer with ReID head
            num_features = backbone.fc.in_features
            backbone.fc = nn.Sequential(
                nn.Linear(num_features, 1024),
                nn.BatchNorm1d(1024),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(1024, self.feature_dim),
                nn.BatchNorm1d(self.feature_dim)
            )
            
            self.model = backbone.to(device)
            self.model.eval()
            self.model_type = "ResNet50-ImageNet"
            
            print("✅ ResNet50 + ImageNet weights loaded successfully!")
            return True
            
        except Exception as e:
            print(f"⚠️ ResNet50 loading failed: {e}")
            return False
    
    def _try_download_reid_weights(self):
        """Try downloading real ReID pre-trained weights."""
        try:
            print("📥 Attempting to download real ReID weights...")
            
            # Create weights directory
            weights_dir = Path(WEIGHTS_DIR)
            weights_dir.mkdir(exist_ok=True)
            
            for model_info in REID_MODEL_URLS:
                weight_path = weights_dir / model_info['name']
                
                try:
                    if not weight_path.exists():
                        print(f"📥 Downloading {model_info['type']}...")
                        
                        # Create SSL context that doesn't verify certificates
                        ssl_context = ssl.create_default_context()
                        ssl_context.check_hostname = False
                        ssl_context.verify_mode = ssl.CERT_NONE
                        
                        response = requests.get(model_info['url'], timeout=60, verify=False)
                        response.raise_for_status()

                        with open(weight_path, 'wb') as f:
                            f.write(response.content)

                        print(f"✅ Downloaded {model_info['type']} ({weight_path.stat().st_size / 1024 / 1024:.1f}MB)")
                    
                    # Try loading the model
                    if self._load_downloaded_weights(weight_path, model_info['type']):
                        return True
                        
                except Exception as e:
                    print(f"⚠️ {model_info['type']} failed: {e}")
                    continue
            
            return False
            
        except Exception as e:
            print(f"⚠️ ReID weight download failed: {e}")
            return False
    
    def _load_downloaded_weights(self, weight_path, model_type):
        """Load downloaded pre-trained weights."""
        try:
            print(f"🔄 Loading {model_type} weights...")
            
            # Create model architecture
            if 'ibn' in model_type.lower():
                # For IBN-Net architecture (if available)
                try:
                    # This would require the IBN-Net implementation
                    # For now, fallback to standard ResNet50
                    raise ImportError("IBN-Net not available")
                except ImportError:
                    model = models.resnet50(pretrained=False)
            else:
                model = models.resnet50(pretrained=False)
            
            # Modify for ReID
            num_features = model.fc.in_features
            model.fc = nn.Sequential(
                nn.Linear(num_features, self.feature_dim),
                nn.BatchNorm1d(self.feature_dim)
            )
            
            # Load weights
            checkpoint = torch.load(weight_path, map_location='cpu')
            
            # Handle different checkpoint formats
            if isinstance(checkpoint, dict):
                if 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                elif 'model' in checkpoint:
                    state_dict = checkpoint['model']
                else:
                    state_dict = checkpoint
            else:
                state_dict = checkpoint
            
            # Remove module prefix if present
            new_state_dict = {}
            for k, v in state_dict.items():
                name = k.replace('module.', '') if k.startswith('module.') else k
                new_state_dict[name] = v
            
            # Load with flexibility for missing keys
            model.load_state_dict(new_state_dict, strict=False)
            
            self.model = model.to(device)
            self.model.eval()
            self.model_type = model_type
            
            print(f"✅ {model_type} loaded successfully!")
            return True
            
        except Exception as e:
            print(f"⚠️ Loading {model_type} failed: {e}")
            return False
    
    def _fallback_to_color_features(self):
        """Fallback to reliable color-based features."""
        print("🎨 Falling back to color-based feature extraction...")
        print("   ✅ No deep learning required")
        print("   🎯 Based on clothing colors, patterns, and spatial layout")
        
        self.model = None  # No neural network model
        self.model_type = "Color-Histogram-Features"
        self.feature_dim = COLOR_FEATURE_CONFIG['feature_dimension']
        
        print("✅ Color-based ReID ready!")
    
    def extract_features(self, image, bbox):
        """Extract ReID features using the best available method."""
        try:
            # Crop person region
            x1, y1, x2, y2 = map(int, bbox)
            x1, y1 = max(0, x1), max(0, y1)
            x2 = min(image.shape[1], x2)
            y2 = min(image.shape[0], y2)
            
            if x2 <= x1 or y2 <= y1:
                return None
            
            person_crop = image[y1:y2, x1:x2]
            if person_crop.size == 0:
                return None
            
            # Extract features based on available model
            if self.model is not None:
                # Deep learning based extraction
                return self._extract_deep_features(person_crop)
            else:
                # Color-based extraction
                return self._extract_color_features(person_crop, bbox)
                
        except Exception as e:
            print(f"⚠️ Feature extraction error: {e}")
            return None
    
    def _extract_deep_features(self, person_crop):
        """Extract features using deep learning model."""
        try:
            # Preprocess
            input_tensor = self.transform(person_crop).unsqueeze(0).to(device)
            
            # Extract features
            with torch.no_grad():
                features = self.model(input_tensor)
                
                # Handle different output formats
                if len(features.shape) > 2:
                    features = F.adaptive_avg_pool2d(features, (1, 1)).flatten(1)
                
                # Normalize
                features = F.normalize(features, p=2, dim=1)
            
            return features.cpu().numpy().flatten()
            
        except Exception as e:
            print(f"⚠️ Deep feature extraction error: {e}")
            return None

    def _extract_color_features(self, person_crop, bbox):
        """Extract color-based features (fallback method)."""
        try:
            # Resize to standard ReID size
            person_resized = cv2.resize(person_crop, PERSON_RESIZE_SIZE)

            features = []

            # 1. RGB color histograms in body regions
            h, w = person_resized.shape[:2]
            regions = {
                'head': person_resized[0:h//3, :],                # Top third
                'torso': person_resized[h//3:2*h//3, :],         # Middle third
                'legs': person_resized[2*h//3:h, :],             # Bottom third
                'left_half': person_resized[:, 0:w//2],          # Left half
                'right_half': person_resized[:, w//2:w],         # Right half
                'center': person_resized[h//4:3*h//4, w//4:3*w//4]  # Center region
            }

            for region_name, region in regions.items():
                if region.size > 0:
                    # RGB histogram for each channel
                    for channel in range(3):  # BGR
                        hist, _ = np.histogram(
                            region[:,:,channel],
                            bins=COLOR_FEATURE_CONFIG['histogram_bins'],
                            range=COLOR_FEATURE_CONFIG['histogram_range']
                        )
                        hist = hist / (np.sum(hist) + 1e-6)
                        features.extend(hist)
                else:
                    features.extend([0] * 24)  # 8 bins × 3 channels

            # 2. HSV color features for better color representation
            hsv = cv2.cvtColor(person_resized, cv2.COLOR_BGR2HSV)

            # Global HSV histogram
            for channel in range(3):
                hist, _ = np.histogram(
                    hsv[:,:,channel],
                    bins=COLOR_FEATURE_CONFIG['histogram_bins'],
                    range=COLOR_FEATURE_CONFIG['histogram_range']
                )
                hist = hist / (np.sum(hist) + 1e-6)
                features.extend(hist)

            # 3. Texture and edge features
            gray = cv2.cvtColor(person_resized, cv2.COLOR_BGR2GRAY)

            # Edge density
            edges = cv2.Canny(
                gray,
                COLOR_FEATURE_CONFIG['canny_low_threshold'],
                COLOR_FEATURE_CONFIG['canny_high_threshold']
            )
            edge_density = np.sum(edges > 0) / edges.size
            features.append(edge_density)

            # Gradient statistics
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=COLOR_FEATURE_CONFIG['sobel_kernel_size'])
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=COLOR_FEATURE_CONFIG['sobel_kernel_size'])
            features.extend([
                np.mean(np.abs(grad_x)),
                np.std(grad_x),
                np.mean(np.abs(grad_y)),
                np.std(grad_y)
            ])

            # 4. Enhanced spatial features - bbox and body characteristics
            x1, y1, x2, y2 = bbox
            bbox_width = x2 - x1
            bbox_height = y2 - y1
            aspect_ratio = bbox_width / max(bbox_height, 1)

            # Body proportions (normalized)
            spatial_features = [
                aspect_ratio,                           # Body width/height ratio
                bbox_width / max(person_resized.shape[1], 1),  # Relative width
                bbox_height / max(person_resized.shape[0], 1), # Relative height
                (bbox_width * bbox_height) / max(person_resized.shape[0] * person_resized.shape[1], 1)  # Relative area
            ]

            # Body region intensity patterns (clothing/skin detection)
            head_region = regions['head']
            torso_region = regions['torso']
            legs_region = regions['legs']

            # Average brightness in each region (helps distinguish clothing)
            if head_region.size > 0:
                spatial_features.append(np.mean(cv2.cvtColor(head_region, cv2.COLOR_BGR2GRAY)))
            else:
                spatial_features.append(0)

            if torso_region.size > 0:
                spatial_features.append(np.mean(cv2.cvtColor(torso_region, cv2.COLOR_BGR2GRAY)))
            else:
                spatial_features.append(0)

            if legs_region.size > 0:
                spatial_features.append(np.mean(cv2.cvtColor(legs_region, cv2.COLOR_BGR2GRAY)))
            else:
                spatial_features.append(0)

            # Clothing pattern detection (edge density in regions)
            for region_name, region in [('torso', torso_region), ('legs', legs_region)]:
                if region.size > 0:
                    gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
                    edges = cv2.Canny(gray_region, 50, 150)
                    edge_density = np.sum(edges > 0) / max(edges.size, 1)
                    spatial_features.append(edge_density)
                else:
                    spatial_features.append(0)

            features.extend(spatial_features)

            # Total: ~220+ dimensions (enhanced spatial features)
            feature_vector = np.array(features)

            # Normalize to unit vector
            norm = np.linalg.norm(feature_vector)
            if norm > 0:
                feature_vector = feature_vector / norm

            return feature_vector

        except Exception as e:
            print(f"⚠️ Color feature extraction error: {e}")
            return None
