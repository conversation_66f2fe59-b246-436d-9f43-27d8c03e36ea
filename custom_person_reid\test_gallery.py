#!/usr/bin/env python3
"""
Test script for the Person Gallery functionality
Demonstrates the new gallery feature without requiring video input
"""

import cv2
import numpy as np
import time
from tracker import ProductionPersonTracker
from person import ProductionPerson

def create_test_person_image(person_id, color_base=None):
    """Create a test person image with unique visual characteristics."""
    if color_base is None:
        # Generate unique colors based on person ID
        np.random.seed(person_id * 42)  # Consistent colors per ID
        color_base = np.random.randint(50, 200, 3)
    
    # Create a person-like image (150x200 to simulate typical person detection)
    img = np.zeros((200, 150, 3), dtype=np.uint8)
    
    # Fill with base color
    img[:] = color_base
    
    # Add some variation to simulate clothing/features
    # Head area (lighter)
    head_color = np.clip(color_base + 30, 0, 255)
    img[20:80, 50:100] = head_color
    
    # Body area (darker)
    body_color = np.clip(color_base - 20, 0, 255)
    img[80:180, 40:110] = body_color
    
    # Add person ID text
    cv2.putText(img, f"Person {person_id}", (10, 190), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    return img

def test_gallery_functionality():
    """Test the person gallery functionality."""
    print("🧪 Testing Person Gallery Functionality")
    print("=" * 50)
    
    # Create tracker
    tracker = ProductionPersonTracker()
    
    # Create test persons with different quality images
    test_persons = []
    for i in range(1, 6):  # Create 5 test persons
        person = ProductionPerson(i)
        
        # Create multiple images with different qualities
        for quality_level in [0.3, 0.6, 0.9]:  # Low, medium, high quality
            test_image = create_test_person_image(i)
            
            # Simulate quality by adding noise for lower quality
            if quality_level < 0.9:
                noise_level = int((1.0 - quality_level) * 50)
                noise = np.random.randint(-noise_level, noise_level, test_image.shape, dtype=np.int16)
                test_image = np.clip(test_image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            # Store as best image if it's the highest quality
            if quality_level > person.best_image_quality:
                person.best_image = test_image.copy()
                person.best_image_quality = quality_level
        
        test_persons.append(person)
        
        # Store in tracker's gallery
        tracker._store_person_image(i, person)
        print(f"✅ Created test person {i} with quality {person.best_image_quality:.1f}")
    
    print(f"\n🖼️ Gallery contains {len(tracker.person_gallery)} persons")
    
    # Create and display gallery
    gallery = tracker._create_gallery_display()
    
    if gallery is not None:
        print(f"✅ Gallery created successfully!")
        print(f"   📐 Gallery dimensions: {gallery.shape}")
        print(f"   🎨 Gallery contains {len(tracker.person_gallery)} person images")
        
        # Display the gallery
        cv2.imshow("Person Gallery Test", gallery)
        print("\n🖼️ Gallery window opened!")
        print("   Press any key to close the gallery...")
        
        # Wait for key press
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
        print("✅ Gallery test completed successfully!")
        
    else:
        print("❌ Gallery creation failed!")
    
    # Test toggle functionality
    print("\n🔄 Testing gallery toggle functionality...")
    initial_state = tracker.show_gallery
    tracker._toggle_gallery()
    print(f"   Gallery state changed: {initial_state} → {tracker.show_gallery}")
    
    tracker._toggle_gallery()
    print(f"   Gallery state changed: {tracker.show_gallery} → {initial_state}")
    
    print("\n🎉 All gallery functionality tests passed!")

def demonstrate_gallery_features():
    """Demonstrate key gallery features."""
    print("\n🎯 Gallery Feature Demonstration")
    print("=" * 40)
    
    features = [
        "✅ Automatic best-quality image selection",
        "✅ Grid layout with up to 4 columns",
        "✅ Person ID labeling",
        "✅ Real-time gallery updates",
        "✅ Toggle show/hide functionality",
        "✅ Quality-based image storage",
        "✅ Session persistence"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n🎮 Controls:")
    print("   Press 'g' during tracking to toggle gallery")
    print("   Gallery window shows all unique persons detected")
    print("   Each person shows their best quality image")

if __name__ == "__main__":
    try:
        test_gallery_functionality()
        demonstrate_gallery_features()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
