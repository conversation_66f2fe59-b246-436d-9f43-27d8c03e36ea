# Main Tracker Module
# Production-ready person tracking system

import cv2
import numpy as np
import time
from collections import deque
from ultralytics import <PERSON>OL<PERSON>
import supervision as sv
import math

from config import (
    MODEL_PATH, CONFIDENCE_THRESHOLD, SIMILARITY_THRESHOLD, B<PERSON>TETRACK_CONFIG,
    DISPLAY_CONFIG, PERFORMANCE_CONFIG, TRACKING_OPTIMIZATION,
    MIN_FRAMES_TO_CONFIRM, MIN_VECTORS_TO_CONFIRM, MAX_MISSING_FRAMES
)
from reid_extractor import ProductionReIDExtractor
from person import ProductionPerson


class ProductionPersonTracker:
    """Production-ready person tracking system."""
    
    def __init__(self):
        print("🚀 Initializing Production Person Tracker")
        print("🎯 Features: Real pre-trained ReID + Robust tracking")
        
        # Initialize YOLO and ByteTracker
        self.yolo_model = YOLO(MODEL_PATH)
        self.tracker = sv.ByteTrack(**BYTETRACK_CONFIG)
        
        # Initialize ReID extractor
        self.reid_extractor = ProductionReIDExtractor()
        
        # Visualization
        self.box_annotator = sv.BoxAnnotator(
            thickness=DISPLAY_CONFIG['box_thickness'], 
            color_lookup=sv.ColorLookup.TRACK
        )
        self.label_annotator = sv.LabelAnnotator(
            text_thickness=DISPLAY_CONFIG['text_thickness'], 
            text_scale=DISPLAY_CONFIG['text_scale']
        )
        
        # Person tracking
        self.pending_persons = {}       # track_id -> ProductionPerson (not yet confirmed)
        self.confirmed_persons = {}     # track_id -> ProductionPerson (confirmed unique)
        self.historical_persons = deque(maxlen=PERFORMANCE_CONFIG['max_historical_persons'])
        
        # Statistics
        self.frame_count = 0
        self.unique_count = 0
        self.reid_matches = 0
        self.safety_confirmations = 0
        self.processing_times = deque(maxlen=PERFORMANCE_CONFIG['max_processing_times'])

        # Optimization tracking
        self.reid_extractions_saved = 0
        self.lightweight_updates = 0

        # Person gallery
        self.person_gallery = {}  # track_id -> best_image
        self.show_gallery = False
        self.gallery_window_name = "Person Gallery"
        
        print("✅ Production tracker initialized!")
        print(f"   🧠 ReID Model: {self.reid_extractor.model_type}")
        print(f"   📊 Feature Dimension: {self.reid_extractor.feature_dim}D")
    
    def process_frame(self, frame):
        """Process single frame with production-quality tracking."""
        start_time = time.time()
        
        # YOLO detection and ByteTracker tracking
        results = self.yolo_model(frame, conf=CONFIDENCE_THRESHOLD, classes=[0], verbose=False)[0]
        detections = sv.Detections.from_ultralytics(results)
        detections = self.tracker.update_with_detections(detections)
        
        self.frame_count += 1
        seen_track_ids = set()
        
        # Process each detected person
        if detections.tracker_id is not None:
            for i, track_id in enumerate(detections.tracker_id):
                seen_track_ids.add(track_id)
                bbox = detections.xyxy[i]
                confidence = detections.confidence[i] if detections.confidence is not None else 0.8
                
                # Update or create person with optimization
                if track_id in self.confirmed_persons:
                    # Use optimized tracking for confirmed persons
                    person = self.confirmed_persons[track_id]

                    if TRACKING_OPTIMIZATION['lightweight_tracking'] and person.should_update_reid_features():
                        # Full ReID update (periodic refresh)
                        person.add_observation(frame, bbox, confidence, self.reid_extractor)
                        print(f"🔄 Person {track_id}: Periodic ReID refresh")
                    else:
                        # Lightweight tracking without ReID
                        person.add_lightweight_observation(frame, bbox, confidence)
                        self.reid_extractions_saved += 1
                        self.lightweight_updates += 1

                elif track_id in self.pending_persons:
                    # Update pending person
                    self.pending_persons[track_id].add_observation(
                        frame, bbox, confidence, self.reid_extractor
                    )
                    
                    # Check if ready for uniqueness decision
                    if self.pending_persons[track_id].is_ready_for_decision():
                        self._make_uniqueness_decision(track_id)
                else:
                    # Create new pending person
                    self.pending_persons[track_id] = ProductionPerson(track_id)
                    self.pending_persons[track_id].add_observation(
                        frame, bbox, confidence, self.reid_extractor
                    )
                    print(f"🔍 New person detected: ID {track_id}")
        
        # Handle persons not seen in current frame
        all_track_ids = set(list(self.pending_persons.keys()) + list(self.confirmed_persons.keys()))
        for track_id in all_track_ids:
            if track_id not in seen_track_ids:
                if track_id in self.pending_persons:
                    self.pending_persons[track_id].mark_missing()
                    # Check for safety confirmation
                    if self.pending_persons[track_id].should_safety_confirm():
                        self._safety_confirm_person(track_id)
                elif track_id in self.confirmed_persons:
                    self.confirmed_persons[track_id].mark_missing()
        
        # Cleanup old persons
        self._cleanup_old_persons()
        
        # Track processing time
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        
        return self._create_visualization(frame, detections)
    
    def _make_uniqueness_decision(self, track_id):
        """Make uniqueness decision using ReID features."""
        if track_id not in self.pending_persons:
            return
        
        pending_person = self.pending_persons[track_id]
        is_unique = True
        max_similarity = 0.0
        matched_person_id = None
        
        summary = pending_person.get_summary()
        print(f"\n🎯 UNIQUENESS CHECK for person {track_id}:")
        print(f"   📊 Observations: {summary['frame_count']}F, {summary['reid_vectors']}V")
        print(f"   🎯 Quality: {summary['avg_quality']:.3f}, Confidence: {summary['avg_confidence']:.3f}")
        
        # Check against confirmed persons
        print(f"   🔍 Checking against {len(self.confirmed_persons)} confirmed persons...")
        for confirmed_id, confirmed_person in self.confirmed_persons.items():
            similarity = pending_person.similarity_to(confirmed_person)
            max_similarity = max(max_similarity, similarity)
            
            print(f"      📊 vs Confirmed {confirmed_id}: {similarity:.4f} ({'MATCH' if similarity > SIMILARITY_THRESHOLD else 'OK'})")
            
            if similarity > SIMILARITY_THRESHOLD:
                is_unique = False
                matched_person_id = confirmed_id
                break
        
        # Check against historical persons if still appears unique
        if is_unique and len(self.historical_persons) > 0:
            print(f"   🔍 Checking against {len(self.historical_persons)} historical persons...")
            recent_historical = list(self.historical_persons)[-PERFORMANCE_CONFIG['recent_historical_check']:]
            
            for historical_person in recent_historical:
                similarity = pending_person.similarity_to(historical_person)
                max_similarity = max(max_similarity, similarity)
                
                print(f"      📊 vs Historical {historical_person.track_id}: {similarity:.4f}")
                
                if similarity > SIMILARITY_THRESHOLD:
                    is_unique = False
                    matched_person_id = historical_person.track_id
                    break
        
        print(f"   📈 Max similarity: {max_similarity:.4f}, Threshold: {SIMILARITY_THRESHOLD}")
        
        # Make final decision
        if is_unique:
            # Confirm as unique person
            confirmed_person = self.pending_persons.pop(track_id)
            confirmed_person.is_confirmed = True  # Set optimization flag
            self.confirmed_persons[track_id] = confirmed_person
            self.unique_count += 1

            # Store best image for gallery
            self._store_person_image(track_id, confirmed_person)

            print(f"   ✅ CONFIRMED UNIQUE: ID {track_id} (optimization enabled)")
            print(f"   🎉 Total unique persons: {self.unique_count}")
            print(f"   🖼️ Person image stored in gallery")
        else:
            # Remove as duplicate
            removed_person = self.pending_persons.pop(track_id)
            removed_person.matched_to_person_id = matched_person_id
            removed_person.reid_confidence = max_similarity
            self.reid_matches += 1
            
            print(f"   ❌ DUPLICATE DETECTED: ID {track_id} → Person {matched_person_id}")
            print(f"   📊 Similarity: {max_similarity:.4f}, Total matches: {self.reid_matches}")
    
    def _safety_confirm_person(self, track_id):
        """Safety confirm person who is leaving frame before full confirmation."""
        if track_id not in self.pending_persons:
            return
        
        pending_person = self.pending_persons[track_id]
        
        # Move to confirmed with safety flag
        confirmed_person = self.pending_persons.pop(track_id)
        confirmed_person.is_confirmed = True  # Set optimization flag
        self.confirmed_persons[track_id] = confirmed_person
        self.unique_count += 1
        self.safety_confirmations += 1

        # Store best image for gallery
        self._store_person_image(track_id, confirmed_person)
        
        summary = confirmed_person.get_summary()
        print(f"🛡️ SAFETY CONFIRMED: ID {track_id}")
        print(f"   📊 {summary['frame_count']}F, {summary['reid_vectors']}V, Quality: {summary['avg_quality']:.3f}")
        print(f"   🎯 Total unique: {self.unique_count} (Safety confirmations: {self.safety_confirmations})")
        print(f"   🖼️ Person image stored in gallery")

    def _store_person_image(self, track_id, person):
        """Store the best quality image of a confirmed person for gallery display."""
        if hasattr(person, 'best_image') and person.best_image is not None:
            # Use the best image stored in the person object
            self.person_gallery[track_id] = person.best_image.copy()
        elif hasattr(person, 'last_image') and person.last_image is not None:
            # Fallback to last seen image
            self.person_gallery[track_id] = person.last_image.copy()
        else:
            print(f"⚠️ No image available for person {track_id}")

    def _create_gallery_display(self):
        """Create a gallery display showing all unique persons."""
        if not self.person_gallery:
            return None

        # Calculate grid dimensions
        num_persons = len(self.person_gallery)
        cols = min(4, num_persons)  # Max 4 columns
        rows = math.ceil(num_persons / cols)

        # Image dimensions for gallery
        img_width, img_height = 150, 200
        margin = 10

        # Create gallery canvas
        gallery_width = cols * img_width + (cols + 1) * margin
        gallery_height = rows * img_height + (rows + 1) * margin + 40  # Extra space for labels
        gallery = np.zeros((gallery_height, gallery_width, 3), dtype=np.uint8)
        gallery.fill(50)  # Dark gray background

        # Add title
        cv2.putText(gallery, f"Unique Persons Gallery ({num_persons} persons)",
                   (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # Add person images
        for idx, (person_id, image) in enumerate(self.person_gallery.items()):
            row = idx // cols
            col = idx % cols

            # Calculate position
            x = col * img_width + (col + 1) * margin
            y = row * img_height + (row + 1) * margin + 40

            # Resize image to fit gallery slot
            resized_img = cv2.resize(image, (img_width, img_height))

            # Place image in gallery
            gallery[y:y+img_height, x:x+img_width] = resized_img

            # Add person ID label
            label = f"ID: {person_id}"
            cv2.putText(gallery, label, (x + 5, y + img_height + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        return gallery

    def _toggle_gallery(self):
        """Toggle the person gallery display."""
        self.show_gallery = not self.show_gallery
        if not self.show_gallery:
            cv2.destroyWindow(self.gallery_window_name)
        print(f"🖼️ Person gallery: {'ON' if self.show_gallery else 'OFF'}")

    def _cleanup_old_persons(self):
        """Clean up persons that haven't been seen for too long."""
        current_time = time.time()
        
        # Clean pending persons
        to_remove_pending = []
        for track_id, person in self.pending_persons.items():
            if person.should_be_removed():
                to_remove_pending.append(track_id)
        
        for track_id in to_remove_pending:
            removed_person = self.pending_persons.pop(track_id)
            summary = removed_person.get_summary()
            print(f"🗑️ Removed incomplete person: ID {track_id}")
            print(f"   📊 {summary['frame_count']}F, {summary['reid_vectors']}V, Duration: {summary['duration']:.1f}s")
        
        # Clean confirmed persons and move to historical
        to_remove_confirmed = []
        for track_id, person in self.confirmed_persons.items():
            if person.should_be_removed():
                to_remove_confirmed.append(track_id)
        
        for track_id in to_remove_confirmed:
            removed_person = self.confirmed_persons.pop(track_id)
            self.historical_persons.append(removed_person)
            
            summary = removed_person.get_summary()
            print(f"📚 Moved to historical: ID {track_id}")
            print(f"   📊 Duration: {summary['duration']:.1f}s, Vectors: {summary['reid_vectors']}")

    def _create_visualization(self, frame, detections):
        """Create annotated visualization of tracking results."""
        annotated_frame = frame.copy()

        # Draw detection boxes and labels
        if len(detections) > 0:
            annotated_frame = self.box_annotator.annotate(annotated_frame, detections)

            # Create enhanced labels with status information
            labels = []
            if detections.tracker_id is not None:
                for track_id in detections.tracker_id:
                    if track_id in self.confirmed_persons:
                        person = self.confirmed_persons[track_id]
                        quality = np.mean(list(person.quality_scores)) if person.quality_scores else 0.0
                        labels.append(f"✅ P{track_id} (Q:{quality:.2f})")
                    elif track_id in self.pending_persons:
                        person = self.pending_persons[track_id]
                        progress = f"{person.frame_count}F/{len(person.reid_features)}V"
                        labels.append(f"🔍 P{track_id} ({progress})")
                    else:
                        labels.append(f"🆕 P{track_id}")

            annotated_frame = self.label_annotator.annotate(annotated_frame, detections, labels)

        # Add comprehensive statistics overlay
        self._add_statistics_overlay(annotated_frame)

        return annotated_frame

    def _add_statistics_overlay(self, frame):
        """Add comprehensive statistics overlay to frame."""
        # Calculate performance metrics
        avg_processing_time = np.mean(list(self.processing_times)) if self.processing_times else 0.0
        current_fps = 1.0 / avg_processing_time if avg_processing_time > 0 else 0.0

        # Main statistics
        main_stats = [
            f"Frame: {self.frame_count}",
            f"FPS: {current_fps:.1f}",
            f"Current: {len(self.pending_persons) + len(self.confirmed_persons)}",
            f"Pending: {len(self.pending_persons)}",
            f"Confirmed: {len(self.confirmed_persons)}",
            f"Total Unique: {self.unique_count}",
            f"ReID Matches: {self.reid_matches}",
            f"Safety: {self.safety_confirmations}",
            f"⚡ Saved: {self.reid_extractions_saved}"
        ]

        # Draw main statistics
        for i, stat in enumerate(main_stats):
            y_pos = 30 + i * 25
            cv2.putText(frame, stat, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # Model information
        model_info = [
            f"ReID Model: {self.reid_extractor.model_type}",
            f"Features: {self.reid_extractor.feature_dim}D",
            f"Threshold: {SIMILARITY_THRESHOLD}",
            f"Proc Time: {avg_processing_time*1000:.1f}ms"
        ]

        # Draw model information on the right side
        for i, info in enumerate(model_info):
            y_pos = 30 + i * 25
            cv2.putText(frame, info, (frame.shape[1] - 400, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

        # Draw pending persons details (if any)
        if self.pending_persons:
            pending_y_start = 250
            cv2.putText(frame, "Pending Persons:", (10, pending_y_start),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

            for i, (track_id, person) in enumerate(list(self.pending_persons.items())[:5]):
                summary = person.get_summary()
                pending_info = f"ID{track_id}: {summary['frame_count']}F/{summary['reid_vectors']}V (Q:{summary['avg_quality']:.2f})"
                cv2.putText(frame, pending_info, (10, pending_y_start + 25 + i * 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    def get_tracking_statistics(self):
        """Get comprehensive tracking statistics."""
        avg_processing_time = np.mean(list(self.processing_times)) if self.processing_times else 0.0

        return {
            'frame_count': self.frame_count,
            'unique_persons': self.unique_count,
            'reid_matches': self.reid_matches,
            'safety_confirmations': self.safety_confirmations,
            'pending_persons': len(self.pending_persons),
            'confirmed_persons': len(self.confirmed_persons),
            'historical_persons': len(self.historical_persons),
            'avg_processing_time_ms': avg_processing_time * 1000,
            'estimated_fps': 1.0 / avg_processing_time if avg_processing_time > 0 else 0.0,
            'reid_model': self.reid_extractor.model_type,
            'feature_dimension': self.reid_extractor.feature_dim,
            'similarity_threshold': SIMILARITY_THRESHOLD,
            'reid_extractions_saved': self.reid_extractions_saved,
            'lightweight_updates': self.lightweight_updates,
            'optimization_enabled': TRACKING_OPTIMIZATION['skip_reid_for_confirmed']
        }

    def run(self, video_path):
        """Run the production person tracking system."""
        print(f"🎬 Opening video: {video_path}")
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            raise ValueError(f"❌ Could not open video: {video_path}")

        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        print(f"📹 Video properties:")
        print(f"   📐 Resolution: {width}×{height}")
        print(f"   🎬 FPS: {fps:.1f}")
        print(f"   📊 Total frames: {total_frames}")
        print(f"   ⏱️ Duration: {total_frames/fps:.1f} seconds")

        # Create display window
        cv2.namedWindow(DISPLAY_CONFIG['window_name'], cv2.WINDOW_NORMAL)
        cv2.resizeWindow(DISPLAY_CONFIG['window_name'],
                        DISPLAY_CONFIG['window_width'],
                        DISPLAY_CONFIG['window_height'])

        print("\n🚀 Production Person Tracking ACTIVE!")
        print(f"🧠 ReID Model: {self.reid_extractor.model_type}")
        print(f"📊 Features: {self.reid_extractor.feature_dim}D")
        print("🎯 Real-time unique person counting with state-of-the-art ReID")
        print("▶️  Press 'q' to quit, 's' for statistics, 'g' to toggle gallery")
        print("-" * 60)

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    # Loop video for continuous demonstration
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    print("🔄 Video restarted")
                    continue

                # Process frame
                annotated_frame = self.process_frame(frame)

                # Display result
                cv2.imshow(DISPLAY_CONFIG['window_name'], annotated_frame)

                # Display gallery if enabled
                if self.show_gallery:
                    gallery_frame = self._create_gallery_display()
                    if gallery_frame is not None:
                        cv2.imshow(self.gallery_window_name, gallery_frame)

                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("\n⏹️ Stopping tracking...")
                    break
                elif key == ord('s'):
                    self._print_detailed_statistics()
                elif key == ord('g'):
                    self._toggle_gallery()

                # Progress update every configured interval
                if self.frame_count % PERFORMANCE_CONFIG['progress_update_interval'] == 0:
                    progress = (self.frame_count % total_frames) / total_frames * 100
                    stats = self.get_tracking_statistics()
                    print(f"📊 Progress: {progress:.1f}% | Unique: {stats['unique_persons']} | "
                          f"Matches: {stats['reid_matches']} | FPS: {stats['estimated_fps']:.1f}")

        except KeyboardInterrupt:
            print("\n⏹️ Tracking interrupted by user")

        finally:
            cap.release()
            cv2.destroyAllWindows()
            self._print_final_results()

    def _print_detailed_statistics(self):
        """Print detailed tracking statistics."""
        stats = self.get_tracking_statistics()

        print("\n" + "="*60)
        print("📊 DETAILED TRACKING STATISTICS")
        print("="*60)
        print(f"🎬 Video Processing:")
        print(f"   Frames processed: {stats['frame_count']}")
        print(f"   Average FPS: {stats['estimated_fps']:.2f}")
        print(f"   Processing time: {stats['avg_processing_time_ms']:.1f}ms per frame")

        print(f"\n👥 Person Tracking:")
        print(f"   Total unique persons: {stats['unique_persons']}")
        print(f"   ReID matches found: {stats['reid_matches']}")
        print(f"   Safety confirmations: {stats['safety_confirmations']}")
        print(f"   Currently pending: {stats['pending_persons']}")
        print(f"   Currently confirmed: {stats['confirmed_persons']}")
        print(f"   Historical persons: {stats['historical_persons']}")

        print(f"\n🧠 ReID System:")
        print(f"   Model: {stats['reid_model']}")
        print(f"   Feature dimension: {stats['feature_dimension']}D")
        print(f"   Similarity threshold: {stats['similarity_threshold']}")

        print(f"\n⚡ Performance Optimization:")
        print(f"   Optimization enabled: {stats['optimization_enabled']}")
        print(f"   ReID extractions saved: {stats['reid_extractions_saved']}")
        print(f"   Lightweight updates: {stats['lightweight_updates']}")
        if stats['lightweight_updates'] > 0:
            efficiency_gain = (stats['reid_extractions_saved'] / (stats['frame_count'] - stats['reid_extractions_saved'] + stats['reid_extractions_saved'])) * 100
            print(f"   Processing efficiency gain: {efficiency_gain:.1f}%")

        # Calculate accuracy metrics
        total_decisions = stats['unique_persons'] + stats['reid_matches']
        if total_decisions > 0:
            unique_rate = (stats['unique_persons'] / total_decisions) * 100
            match_rate = (stats['reid_matches'] / total_decisions) * 100
            print(f"\n📈 Performance Metrics:")
            print(f"   Unique person rate: {unique_rate:.1f}%")
            print(f"   Duplicate detection rate: {match_rate:.1f}%")
            print(f"   Safety confirmation rate: {(stats['safety_confirmations'] / stats['unique_persons'] * 100):.1f}%")

        print("="*60)

    def _print_final_results(self):
        """Print final tracking results."""
        stats = self.get_tracking_statistics()

        print("\n" + "🎯" + "="*58 + "🎯")
        print("🏁 FINAL PRODUCTION TRACKING RESULTS")
        print("🎯" + "="*58 + "🎯")

        print(f"📊 Processing Summary:")
        print(f"   Total frames processed: {stats['frame_count']:,}")
        print(f"   Average processing speed: {stats['estimated_fps']:.2f} FPS")
        print(f"   Total processing time: {stats['frame_count'] * stats['avg_processing_time_ms'] / 1000:.1f} seconds")

        print(f"\n👥 Person Counting Results:")
        print(f"   🎉 TOTAL UNIQUE PERSONS: {stats['unique_persons']}")
        print(f"   🔄 ReID matches detected: {stats['reid_matches']}")
        print(f"   🛡️ Safety confirmations: {stats['safety_confirmations']}")

        print(f"\n🧠 ReID System Performance:")
        print(f"   Model used: {stats['reid_model']}")
        print(f"   Feature dimension: {stats['feature_dimension']}D")
        print(f"   Similarity threshold: {stats['similarity_threshold']}")

        # Final accuracy calculation
        total_decisions = stats['unique_persons'] + stats['reid_matches']
        if total_decisions > 0:
            accuracy = (stats['reid_matches'] / total_decisions) * 100
            print(f"   ReID accuracy estimate: {accuracy:.1f}%")

        print(f"\n🚀 System Capabilities Demonstrated:")
        print(f"   ✅ Real-time person detection and tracking")
        print(f"   ✅ Advanced ReID with pre-trained features")
        print(f"   ✅ Duplicate person detection and prevention")
        print(f"   ✅ Robust handling of occlusions and re-entries")
        print(f"   ✅ Safety mechanisms for edge cases")

        print("🎯" + "="*58 + "🎯")
