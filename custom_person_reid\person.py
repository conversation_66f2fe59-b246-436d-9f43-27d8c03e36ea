# Person Data Module
# Production-ready person data with robust tracking

import numpy as np
import time
from collections import deque

from config import (
    MIN_FRAMES_TO_CONFIRM, MIN_VECTORS_TO_CONFIRM, MAX_MISSING_FRAMES,
    SAFETY_CONFIRMATION_FRAMES, DEQUE_SIZES, QUALITY_WEIGHTS, SIMILARITY_WEIGHTS,
    TRACKING_OPTIMIZATION
)


class ProductionPerson:
    """Production-ready person data with robust tracking."""
    
    def __init__(self, track_id):
        self.track_id = track_id
        self.reid_features = deque(maxlen=DEQUE_SIZES['reid_features'])
        self.frame_count = 0
        self.missing_frames = 0
        self.first_seen = time.time()
        self.last_seen = time.time()
        
        # Quality tracking
        self.confidences = deque(maxlen=DEQUE_SIZES['confidences'])
        self.bbox_sizes = deque(maxlen=DEQUE_SIZES['bbox_sizes'])
        self.quality_scores = deque(maxlen=DEQUE_SIZES['quality_scores'])
        
        # Movement tracking
        self.positions = deque(maxlen=DEQUE_SIZES['positions'])
        self.velocities = deque(maxlen=DEQUE_SIZES['velocities'])
        
        # ReID status
        self.matched_to_person_id = None
        self.reid_confidence = 0.0

        # Optimization tracking
        self.frames_since_reid_update = 0
        self.total_observations = 0
        self.is_confirmed = False

        # Quality assurance system
        self.frames_since_quality_check = 0
        self.quality_vectors = deque(maxlen=TRACKING_OPTIMIZATION['max_quality_vectors'])
        self.last_quality_check_time = time.time()
        self.quality_check_needed = False

        # Image storage for gallery
        self.best_image = None
        self.best_image_quality = 0.0
        self.last_image = None

        # Reduced spam - only print person creation for first few persons
        if track_id <= 5:  # Only for first 5 persons
            print(f"👤 Created person: ID {track_id}")
    
    def add_observation(self, image, bbox, confidence, reid_extractor):
        """Add new observation with quality assessment."""
        # Extract person image from bbox
        x1, y1, x2, y2 = map(int, bbox)
        person_image = image[y1:y2, x1:x2].copy()
        self.last_image = person_image

        # Extract ReID features
        reid_features = reid_extractor.extract_features(image, bbox)
        
        if reid_features is not None:
            self.reid_features.append(reid_features)
            
            # Calculate quality score
            quality = self._calculate_quality_score(bbox, confidence, reid_features)
            self.quality_scores.append(quality)

            # Store best quality image for gallery
            if quality > self.best_image_quality:
                self.best_image_quality = quality
                self.best_image = person_image.copy()

            # Only print for first few features or significant quality changes
            if len(self.reid_features) <= 3 or quality > self.best_image_quality + 0.05:
                print(f"📊 Person {self.track_id}: Features added (total: {len(self.reid_features)}, quality: {quality:.3f})")
        else:
            print(f"❌ Person {self.track_id}: Feature extraction failed")
        
        # Update tracking info
        x1, y1, x2, y2 = bbox
        center_x, center_y = (x1 + x2) / 2, (y1 + y2) / 2
        bbox_area = (x2 - x1) * (y2 - y1)
        
        # Calculate velocity if we have previous position
        if len(self.positions) > 0:
            prev_x, prev_y = self.positions[-1]
            velocity = np.sqrt((center_x - prev_x)**2 + (center_y - prev_y)**2)
            self.velocities.append(velocity)
        
        self.positions.append((center_x, center_y))
        self.confidences.append(confidence)
        self.bbox_sizes.append(bbox_area)
        
        # Update counters
        self.frame_count += 1
        self.total_observations += 1
        self.frames_since_reid_update += 1
        self.missing_frames = 0
        self.last_seen = time.time()

        # Reset ReID update counter if we extracted features
        if reid_features is not None:
            self.frames_since_reid_update = 0

    def add_lightweight_observation(self, image, bbox, confidence):
        """Add lightweight observation without ReID feature extraction (for confirmed persons)."""
        # Extract and store person image
        x1, y1, x2, y2 = map(int, bbox)
        person_image = image[y1:y2, x1:x2].copy()
        self.last_image = person_image

        # Update tracking info without expensive ReID processing
        center_x, center_y = (x1 + x2) / 2, (y1 + y2) / 2
        bbox_area = (x2 - x1) * (y2 - y1)

        # Calculate velocity if we have previous position
        if len(self.positions) > 0:
            prev_x, prev_y = self.positions[-1]
            velocity = np.sqrt((center_x - prev_x)**2 + (center_y - prev_y)**2)
            self.velocities.append(velocity)

        self.positions.append((center_x, center_y))
        self.confidences.append(confidence)
        self.bbox_sizes.append(bbox_area)

        # Update counters
        self.frame_count += 1
        self.total_observations += 1
        self.frames_since_reid_update += 1
        self.frames_since_quality_check += 1  # Track frames since quality check
        self.missing_frames = 0
        self.last_seen = time.time()

        # Only print lightweight updates occasionally to reduce spam
        if self.frames_since_reid_update % 50 == 0:  # Every 50 frames
            print(f"⚡ Person {self.track_id}: Lightweight update (frames since ReID: {self.frames_since_reid_update})")

    def add_quality_assurance_observation(self, image, bbox, confidence, reid_extractor):
        """Add observation with quality assurance check for confirmed persons."""
        # Extract person image from bbox
        x1, y1, x2, y2 = map(int, bbox)
        person_image = image[y1:y2, x1:x2].copy()
        self.last_image = person_image

        # Extract ReID features for quality check
        reid_features = reid_extractor.extract_features(image, bbox)

        if reid_features is not None:
            # Perform quality check against historical vectors
            vector_added = self.perform_quality_check(reid_features)

            if vector_added:
                # Calculate quality score for the new vector
                quality = self._calculate_quality_score(bbox, confidence, reid_features)
                self.quality_scores.append(quality)

                # Store best quality image for gallery
                if quality > self.best_image_quality:
                    self.best_image_quality = quality
                    self.best_image = person_image.copy()
            else:
                # Add to quality vectors for future comparisons (but not to main reid_features)
                self.quality_vectors.append(reid_features.copy())
        else:
            print(f"❌ Person {self.track_id}: Quality check feature extraction failed")

        # Update tracking info (same as lightweight observation)
        center_x, center_y = (x1 + x2) / 2, (y1 + y2) / 2
        bbox_area = (x2 - x1) * (y2 - y1)

        # Calculate velocity if we have previous position
        if len(self.positions) > 0:
            prev_x, prev_y = self.positions[-1]
            velocity = np.sqrt((center_x - prev_x)**2 + (center_y - prev_y)**2)
            self.velocities.append(velocity)

        self.positions.append((center_x, center_y))
        self.confidences.append(confidence)
        self.bbox_sizes.append(bbox_area)

        # Update counters
        self.frame_count += 1
        self.total_observations += 1
        self.frames_since_reid_update += 1
        self.frames_since_quality_check = 0  # Reset quality check counter
        self.missing_frames = 0
        self.last_seen = time.time()

    def should_update_reid_features(self):
        """Check if ReID features should be updated for confirmed person."""
        if not TRACKING_OPTIMIZATION['skip_reid_for_confirmed']:
            return True

        if not self.is_confirmed:
            return True

        # For confirmed persons, only update ReID features if:
        # 1. They've been missing for a while (to handle re-association)
        # 2. It's been a very long time since last ReID update
        return (self.missing_frames > 0 and
                self.frames_since_reid_update >= TRACKING_OPTIMIZATION['confirmed_update_interval'])

    def _calculate_quality_score(self, bbox, confidence, features):
        """Calculate observation quality score."""
        # Detection confidence factor
        conf_factor = confidence
        
        # Bbox size factor (larger = better)
        x1, y1, x2, y2 = bbox
        bbox_area = (x2 - x1) * (y2 - y1)
        size_factor = min(bbox_area / (150 * 300), 1.0)  # Normalize to reasonable person size
        
        # Feature quality factor (feature magnitude)
        feature_factor = min(np.linalg.norm(features), 1.0)
        
        # Combine factors using configured weights
        quality = (QUALITY_WEIGHTS['confidence_factor'] * conf_factor + 
                  QUALITY_WEIGHTS['size_factor'] * size_factor + 
                  QUALITY_WEIGHTS['feature_factor'] * feature_factor)
        return quality
    
    def mark_missing(self):
        """Mark person as missing in current frame."""
        self.missing_frames += 1
    
    def is_ready_for_decision(self):
        """Check if person has enough data for uniqueness decision."""
        ready = (self.frame_count >= MIN_FRAMES_TO_CONFIRM and 
                len(self.reid_features) >= MIN_VECTORS_TO_CONFIRM)
        
        if ready:
            print(f"✅ Person {self.track_id}: Ready for decision ({self.frame_count}F, {len(self.reid_features)}V)")
        
        return ready
    
    def should_safety_confirm(self):
        """Check if person should be safety confirmed before leaving."""
        return (self.frame_count >= SAFETY_CONFIRMATION_FRAMES and 
                len(self.reid_features) >= 1 and
                self.missing_frames >= MAX_MISSING_FRAMES)
    
    def should_be_removed(self):
        """Check if person should be removed (too long missing)."""
        return self.missing_frames >= MAX_MISSING_FRAMES
    
    def get_robust_reid_features(self):
        """Get robust ReID features using quality weighting."""
        if len(self.reid_features) == 0:
            return None
        
        # Use quality-weighted average
        num_features = len(self.reid_features)
        if len(self.quality_scores) >= num_features:
            weights = np.array(list(self.quality_scores)[-num_features:])
            weights = weights / (np.sum(weights) + 1e-8)  # Normalize weights
            
            # Calculate weighted average
            features_array = np.array(list(self.reid_features))
            robust_features = np.average(features_array, axis=0, weights=weights)
        else:
            # Simple average if quality scores not available
            robust_features = np.mean(list(self.reid_features), axis=0)
        
        # Final normalization
        norm = np.linalg.norm(robust_features)
        if norm > 0:
            robust_features = robust_features / norm
        
        return robust_features
    
    def similarity_to(self, other_person):
        """Calculate similarity to another person using ReID features."""
        my_features = self.get_robust_reid_features()
        other_features = other_person.get_robust_reid_features()
        
        if my_features is None or other_features is None:
            return 0.0
        
        # Primary similarity: cosine similarity of ReID features
        cosine_sim = np.dot(my_features, other_features)
        
        # Quality factor: higher quality comparisons are more trustworthy
        my_quality = np.mean(list(self.quality_scores)) if self.quality_scores else 0.5
        other_quality = np.mean(list(other_person.quality_scores)) if other_person.quality_scores else 0.5
        quality_factor = (my_quality + other_quality) / 2.0
        
        # Size consistency factor
        size_factor = self._calculate_size_consistency(other_person)
        
        # Combined similarity with configured weights
        final_similarity = (
            SIMILARITY_WEIGHTS['cosine_similarity'] * cosine_sim +
            SIMILARITY_WEIGHTS['quality_factor'] * quality_factor +
            SIMILARITY_WEIGHTS['size_factor'] * size_factor
        )
        
        return max(0.0, min(1.0, final_similarity))
    
    def _calculate_size_consistency(self, other_person):
        """Calculate size consistency between persons."""
        if len(self.bbox_sizes) == 0 or len(other_person.bbox_sizes) == 0:
            return 0.5
        
        my_avg_size = np.mean(list(self.bbox_sizes))
        other_avg_size = np.mean(list(other_person.bbox_sizes))
        
        # Size similarity (closer sizes = higher score)
        size_ratio = min(my_avg_size, other_avg_size) / max(my_avg_size, other_avg_size)
        return size_ratio

    def should_perform_quality_check(self):
        """Check if quality assurance is needed for this confirmed person."""
        if not self.is_confirmed:
            return False

        # Must wait minimum frames before first quality check
        if self.frames_since_reid_update < TRACKING_OPTIMIZATION['min_frames_before_quality_check']:
            return False

        # Check if it's time for quality assurance (every 20-30 frames with randomization)
        base_interval = TRACKING_OPTIMIZATION['quality_check_interval']
        # Add randomization ±5 frames to avoid synchronized checks
        import random
        actual_interval = base_interval + random.randint(-5, 5)

        return self.frames_since_quality_check >= actual_interval

    def perform_quality_check(self, current_reid_features):
        """Perform quality assurance check against historical vectors."""
        if not self.quality_vectors or current_reid_features is None:
            return False

        # Compare current features against all quality vectors
        similarities = []
        for quality_vector in self.quality_vectors:
            similarity = np.dot(current_reid_features, quality_vector) / (
                np.linalg.norm(current_reid_features) * np.linalg.norm(quality_vector)
            )
            similarities.append(similarity)

        # Get maximum similarity with historical vectors
        max_similarity = max(similarities) if similarities else 0.0
        threshold = TRACKING_OPTIMIZATION['quality_similarity_threshold']

        # Reset quality check counter
        self.frames_since_quality_check = 0

        # If similarity is below threshold, we need to add a new vector
        if max_similarity < threshold:
            print(f"🔍 Person {self.track_id}: Quality check failed (similarity: {max_similarity:.3f} < {threshold:.1f})")
            print(f"📈 Person {self.track_id}: Adding new quality vector for appearance change")

            # Add current features to quality vectors
            self.quality_vectors.append(current_reid_features.copy())

            # Also add to main reid_features for matching
            self.reid_features.append(current_reid_features.copy())

            return True  # Vector was added
        else:
            # Only print quality check passes occasionally to reduce spam
            if len(self.quality_vectors) <= 2 or max_similarity < 0.85:  # Print for early checks or borderline cases
                print(f"✅ Person {self.track_id}: Quality check passed (similarity: {max_similarity:.3f})")
            return False  # No vector needed

    def get_summary(self):
        """Get person summary for debugging."""
        avg_quality = np.mean(list(self.quality_scores)) if self.quality_scores else 0.0
        avg_confidence = np.mean(list(self.confidences)) if self.confidences else 0.0
        
        return {
            'track_id': self.track_id,
            'frame_count': self.frame_count,
            'reid_vectors': len(self.reid_features),
            'avg_quality': avg_quality,
            'avg_confidence': avg_confidence,
            'duration': self.last_seen - self.first_seen,
            'missing_frames': self.missing_frames
        }
