#!/usr/bin/env python3
"""
Person Gallery Feature Demo
Demonstrates the new person gallery functionality in the tracking system
"""

import cv2
import numpy as np
import time
from tracker import ProductionPersonTracker
from person import Production<PERSON>erson

def create_demo_person_image(person_id):
    """Create a realistic demo person image."""
    # Create base image
    img = np.zeros((200, 120, 3), dtype=np.uint8)
    
    # Generate consistent colors for each person
    np.random.seed(person_id * 123)
    shirt_color = np.random.randint(50, 200, 3)
    pants_color = np.random.randint(30, 150, 3)
    
    # Draw person silhouette
    # Head
    cv2.circle(img, (60, 40), 25, (220, 180, 160), -1)
    
    # Body (shirt)
    cv2.rectangle(img, (35, 65), (85, 120), shirt_color.tolist(), -1)
    
    # Legs (pants)
    cv2.rectangle(img, (40, 120), (80, 180), pants_color.tolist(), -1)
    
    # Add person ID
    cv2.putText(img, f"ID:{person_id}", (5, 195), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    return img

def demo_gallery_features():
    """Demonstrate the person gallery features."""
    print("🎬 Person Gallery Feature Demo")
    print("=" * 50)
    
    # Initialize tracker
    tracker = ProductionPersonTracker()
    
    print("\n📸 Creating demo persons...")
    
    # Create 6 demo persons with different qualities
    demo_persons = []
    for person_id in range(1, 7):
        person = ProductionPerson(person_id)
        
        # Create best quality image
        best_image = create_demo_person_image(person_id)
        person.best_image = best_image
        person.best_image_quality = 0.85 + (person_id * 0.02)  # Varying quality
        
        demo_persons.append(person)
        tracker._store_person_image(person_id, person)
        
        print(f"   ✅ Person {person_id} added (Quality: {person.best_image_quality:.2f})")
    
    print(f"\n🖼️ Gallery Status:")
    print(f"   📊 Total persons: {len(tracker.person_gallery)}")
    print(f"   🎨 Gallery enabled: {tracker.show_gallery}")
    
    # Create gallery display
    print("\n🎨 Creating gallery display...")
    gallery = tracker._create_gallery_display()
    
    if gallery is not None:
        print(f"   ✅ Gallery created: {gallery.shape[1]}x{gallery.shape[0]} pixels")
        
        # Show gallery
        cv2.imshow("Person Gallery Demo", gallery)
        print("\n🖼️ Gallery window opened!")
        print("   📋 Gallery Features Demonstrated:")
        print("      ✅ Grid layout (up to 4 columns)")
        print("      ✅ Person ID labels")
        print("      ✅ Best quality image selection")
        print("      ✅ Automatic sizing and positioning")
        print("      ✅ Professional dark theme")
        
        print("\n⌨️  Press any key to continue...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
        # Test toggle functionality
        print("\n🔄 Testing toggle functionality...")
        print(f"   Initial state: {'ON' if tracker.show_gallery else 'OFF'}")
        
        tracker._toggle_gallery()
        print(f"   After toggle: {'ON' if tracker.show_gallery else 'OFF'}")
        
        tracker._toggle_gallery()
        print(f"   After toggle: {'ON' if tracker.show_gallery else 'OFF'}")
        
        print("\n✅ All gallery features working correctly!")
        
    else:
        print("   ❌ Gallery creation failed")

def show_usage_instructions():
    """Show usage instructions for the gallery feature."""
    print("\n📖 Person Gallery Usage Instructions")
    print("=" * 45)
    
    instructions = [
        "🚀 How to Use the Person Gallery:",
        "",
        "1. 🎬 Start the tracking system:",
        "   python main.py",
        "",
        "2. ⌨️  Use keyboard controls during tracking:",
        "   • Press 'g' to toggle gallery on/off",
        "   • Press 's' for statistics",
        "   • Press 'q' to quit",
        "",
        "3. 🖼️ Gallery Features:",
        "   • Automatically captures best quality image per person",
        "   • Updates in real-time as new persons are detected",
        "   • Shows up to 4 persons per row in grid layout",
        "   • Each image labeled with person tracking ID",
        "   • Gallery persists throughout tracking session",
        "",
        "4. 🎯 Quality Selection:",
        "   • System automatically selects clearest image",
        "   • Based on detection confidence and image quality",
        "   • Updates if better quality image is captured",
        "",
        "5. 💡 Tips:",
        "   • Gallery works best with good lighting",
        "   • Clear, unobstructed person views give best results",
        "   • Gallery can be toggled on/off anytime during tracking"
    ]
    
    for instruction in instructions:
        print(f"   {instruction}")

if __name__ == "__main__":
    try:
        demo_gallery_features()
        show_usage_instructions()
        
        print("\n🎉 Person Gallery Demo Complete!")
        print("   Ready to use in your tracking system!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
