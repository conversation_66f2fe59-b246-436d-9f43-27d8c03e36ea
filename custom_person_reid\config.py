# Configuration file for Production TransReID Person Tracking System
# All configuration constants and parameters

import torch

# VIDEO AND MODEL CONFIGURATION
VIDEO_PATH = r"C:\Users\<USER>\Downloads\aiview_auto_recorder_20220217130214.mp4"
MODEL_PATH = "models/yolov8n.pt"  # Nano model for fastest detection
CONFIDENCE_THRESHOLD = 0.5

# REID PARAMETERS - TUNED FOR PRODUCTION
REID_FEATURE_DIM = 512          # Using ResNet50 features
MIN_FRAMES_TO_CONFIRM = 20       # Balanced requirement
MIN_VECTORS_TO_CONFIRM = 4      # Minimum for averaging
SIMILARITY_THRESHOLD = 0.80     # Tuned for real features
MAX_MISSING_FRAMES = 15
SAFETY_CONFIRMATION_FRAMES = 4

# DEVICE CONFIGURATION
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🔧 CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"🔧 GPU Device: {torch.cuda.get_device_name(0)}")
    print(f"🔧 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

# BYTETRACK CONFIGURATION
BYTETRACK_CONFIG = {
    'track_activation_threshold': CONFIDENCE_THRESHOLD,
    'lost_track_buffer': 30,
    'minimum_matching_threshold': 0.8,
    'frame_rate': 30
}

# VISUALIZATION CONFIGURATION
DISPLAY_CONFIG = {
    'window_name': 'Production Person Tracking',
    'window_width': 1280,
    'window_height': 720,
    'box_thickness': 2,
    'text_thickness': 2,
    'text_scale': 0.7
}

# PERFORMANCE MONITORING
PERFORMANCE_CONFIG = {
    'max_processing_times': 100,
    'max_historical_persons': 100,
    'progress_update_interval': 500,
    'recent_historical_check': 30
}

# TRACKING OPTIMIZATION
TRACKING_OPTIMIZATION = {
    'skip_reid_for_confirmed': True,  # Skip ReID feature extraction for confirmed persons
    'confirmed_update_interval': 30,  # Update ReID features every N frames for confirmed persons (increased from 5)
    'lightweight_tracking': True,     # Use lightweight tracking for confirmed persons
    'reid_refresh_threshold': 60,     # Refresh ReID features if person missing for N frames (increased from 30)
    'skip_reid_matching_for_confirmed': True  # Skip ReID matching entirely for confirmed persons with same track_id
}

# REID MODEL URLS AND PATHS
REID_MODEL_URLS = [
    {
        'url': 'https://github.com/michuanhaohao/reid-strong-baseline/releases/download/v1.0.0/resnet50_ibn_a_market1501.pth',
        'name': 'resnet50_ibn_a_market1501.pth',
        'type': 'ResNet50-IBN-ReID',
        'feature_dim': 2048
    },
    {
        'url': 'https://github.com/KaiyangZhou/deep-person-reid/releases/download/v1.0.0/osnet_x1_0_market1501_256x128_amsgrad_ep150_stp60_lr0.0015_b64_fb10_softmax_labelsmooth_flip.pth',
        'name': 'osnet_x1_0_market1501.pth',
        'type': 'OSNet-Market1501',
        'feature_dim': 512
    },
    {
        'url': 'https://download.pytorch.org/models/resnet50-19c8e357.pth',
        'name': 'resnet50_pytorch.pth',
        'type': 'ResNet50-PyTorch',
        'feature_dim': 2048
    }
]

WEIGHTS_DIR = "reid_weights"

# IMAGE PREPROCESSING CONFIGURATION
REID_IMAGE_SIZE = (256, 128)  # Standard ReID size (height, width)
PERSON_RESIZE_SIZE = (64, 128)  # For color features

# Normalization parameters for ImageNet pre-trained models
IMAGENET_MEAN = [0.485, 0.456, 0.406]
IMAGENET_STD = [0.229, 0.224, 0.225]

# IMAGE STANDARDIZATION FOR LIGHTING ROBUSTNESS
IMAGE_STANDARDIZATION = {
    'enable_histogram_equalization': True,    # Adaptive histogram equalization
    'enable_color_normalization': True,       # Normalize color channels
    'enable_contrast_enhancement': True,      # CLAHE for local contrast
    'enable_brightness_normalization': True,  # Normalize brightness levels
    'clahe_clip_limit': 2.0,                 # CLAHE clipping limit
    'clahe_tile_grid_size': (8, 8),          # CLAHE tile size
    'gamma_correction': 1.2,                 # Gamma correction factor
    'color_temperature_normalization': True,  # White balance correction
    'shadow_highlight_balance': True,         # Balance shadows and highlights
}

# COLOR FEATURE EXTRACTION CONFIGURATION
COLOR_FEATURE_CONFIG = {
    'histogram_bins': 8,
    'histogram_range': [0, 256],
    'feature_dimension': 220,  # Enhanced spatial + color features
    'canny_low_threshold': 50,
    'canny_high_threshold': 150,
    'sobel_kernel_size': 3
}

# QUALITY SCORING WEIGHTS
QUALITY_WEIGHTS = {
    'confidence_factor': 0.5,
    'size_factor': 0.3,
    'feature_factor': 0.2
}

# SIMILARITY CALCULATION WEIGHTS
SIMILARITY_WEIGHTS = {
    'cosine_similarity': 0.80,
    'quality_factor': 0.15,
    'size_factor': 0.05
}

# DEQUE SIZES FOR TRACKING
DEQUE_SIZES = {
    'reid_features': 5,
    'confidences': 10,
    'bbox_sizes': 10,
    'quality_scores': 5,
    'positions': 15,
    'velocities': 10
}

# Print configuration on import
print(f"🔧 Device: {device}")
print(f"🚀 Production ReID: Real pre-trained features")
