# Production TransReID Person Tracking System
# Main entry point for the modular person tracking system

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from config import VIDEO_PATH, device, SIMILARITY_THRESHOLD, MIN_FRAMES_TO_CONFIRM
from tracker import Production<PERSON>ersonTracker


def main():
    """Main execution function."""
    print("🚀 PRODUCTION PERSON TRACKING SYSTEM")
    print("=" * 70)
    print(f"📹 Video: {VIDEO_PATH}")
    print(f"🎯 Similarity threshold: {SIMILARITY_THRESHOLD}")
    print(f"⚙️ Min frames for confirmation: {MIN_FRAMES_TO_CONFIRM}")
    print(f"🔧 Device: {device}")
    print("=" * 70)

    try:
        # Initialize and run tracker
        tracker = ProductionPersonTracker()
        tracker.run(VIDEO_PATH)

    except FileNotFoundError:
        print(f"❌ Error: Video file not found: {VIDEO_PATH}")
        print("💡 Please check the video path and try again.")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()