#!/usr/bin/env python3
"""
Test script to verify OSNet x0.5 integration with our custom person tracking system
"""

import cv2
import numpy as np
import time
from reid_extractor import ProductionReIDExtractor

def create_test_person_image():
    """Create a simple test person image"""
    # Create a 256x128 image (standard ReID input size)
    img = np.zeros((256, 128, 3), dtype=np.uint8)
    
    # Fill with a person-like pattern
    # Head (darker)
    img[0:50, :] = (100, 100, 150)
    
    # Torso (shirt color)
    img[50:150, :] = (50, 150, 200)
    
    # Legs (pants color)
    img[150:256, :] = (80, 80, 120)
    
    # Add some noise for realism
    noise = np.random.randint(-20, 20, img.shape, dtype=np.int16)
    img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    return img

def test_osnet_integration():
    """Test OSNet integration with our ReID extractor"""
    print("🧪 Testing OSNet x0.5 Integration")
    print("=" * 50)
    
    # Initialize the ReID extractor
    print("🔄 Initializing ReID extractor...")
    extractor = ProductionReIDExtractor()
    
    # Create test images
    print("\n📸 Creating test person images...")
    test_images = []
    for i in range(3):
        img = create_test_person_image()
        test_images.append(img)
        print(f"  Created test image {i+1}: {img.shape}")
    
    # Test feature extraction
    print(f"\n🔍 Testing feature extraction...")
    features_list = []
    extraction_times = []
    
    for i, img in enumerate(test_images):
        # Define a bounding box for the entire image
        bbox = [0, 0, img.shape[1], img.shape[0]]
        
        print(f"\n--- Test Image {i+1} ---")
        start_time = time.time()
        
        # Extract features
        features = extractor.extract_features(img, bbox)
        
        extraction_time = time.time() - start_time
        extraction_times.append(extraction_time)
        
        if features is not None:
            features_list.append(features)
            print(f"✅ Features extracted successfully!")
            print(f"   📊 Feature dimension: {features.shape[0]}D")
            print(f"   ⏱️ Extraction time: {extraction_time*1000:.1f}ms")
            print(f"   📈 Feature range: [{features.min():.3f}, {features.max():.3f}]")
            print(f"   🎯 Feature norm: {np.linalg.norm(features):.3f}")
        else:
            print(f"❌ Feature extraction failed!")
    
    # Test feature similarity
    if len(features_list) >= 2:
        print(f"\n📊 Feature Similarity Analysis")
        print("-" * 30)
        
        def compute_cosine_similarity(feat1, feat2):
            """Compute cosine similarity between two feature vectors"""
            feat1_norm = feat1 / (np.linalg.norm(feat1) + 1e-8)
            feat2_norm = feat2 / (np.linalg.norm(feat2) + 1e-8)
            return np.dot(feat1_norm, feat2_norm)
        
        # Compare all pairs
        for i in range(len(features_list)):
            for j in range(i+1, len(features_list)):
                similarity = compute_cosine_similarity(features_list[i], features_list[j])
                print(f"  Image {i+1} vs Image {j+1}: {similarity:.3f}")
    
    # Performance summary
    if extraction_times:
        avg_time = np.mean(extraction_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0
        
        print(f"\n⚡ Performance Summary")
        print("-" * 30)
        print(f"  Average extraction time: {avg_time*1000:.1f}ms")
        print(f"  Theoretical FPS: {fps:.1f}")
        print(f"  Model type: {extractor.model_type}")
        print(f"  Feature dimension: {extractor.feature_dim}D")
    
    # Save test images for visual inspection
    print(f"\n💾 Saving test images...")
    for i, img in enumerate(test_images):
        filename = f"test_person_{i+1}.jpg"
        cv2.imwrite(filename, img)
        print(f"  Saved: {filename}")
    
    return extractor

def test_with_webcam(extractor):
    """Test with webcam if available"""
    print(f"\n📹 Testing with webcam (optional)")
    print("Press 'q' to quit, 's' to skip")
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Webcam not available, skipping...")
        return
    
    print("✅ Webcam opened successfully!")
    print("   Press 'q' to quit")
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Simple person detection (use center region as "person")
        h, w = frame.shape[:2]
        center_bbox = [w//4, h//4, 3*w//4, 3*h//4]
        
        # Extract features every 10 frames
        if frame_count % 10 == 0:
            start_time = time.time()
            features = extractor.extract_features(frame, center_bbox)
            extraction_time = time.time() - start_time
            
            if features is not None:
                fps = 1.0 / extraction_time if extraction_time > 0 else 0
                cv2.putText(frame, f'ReID FPS: {fps:.1f}', (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, f'Features: {features.shape[0]}D', (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, f'Model: {extractor.model_type}', (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # Draw center region
        cv2.rectangle(frame, (center_bbox[0], center_bbox[1]), 
                     (center_bbox[2], center_bbox[3]), (0, 255, 0), 2)
        cv2.putText(frame, 'ReID Test Region', (center_bbox[0], center_bbox[1]-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        cv2.imshow('OSNet ReID Test', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            break
    
    cap.release()
    cv2.destroyAllWindows()

def main():
    """Main test function"""
    print("🚀 OSNet x0.5 Integration Test")
    print("=" * 60)
    
    # Test basic integration
    extractor = test_osnet_integration()
    
    # Test with webcam if available
    try:
        test_with_webcam(extractor)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    
    print(f"\n✅ OSNet integration test completed!")
    print("Check the generated test images to see the synthetic person data used.")

if __name__ == "__main__":
    main()
